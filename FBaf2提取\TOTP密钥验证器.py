#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TOTP密钥验证器
测试4个候选密钥，找出真正的Facebook 2FA密钥
"""

import base64
import hmac
import hashlib
import struct
import time
from datetime import datetime

class TOTPValidator:
    def __init__(self):
        self.candidates = [
            {
                'id': 1,
                'key': 'CBIWZZXTBAOGV7KGD7UUVD4XHIQOXDFO',
                'formatted': 'CBIW ZZXT BAOG V7KG D7UU VD4X HIQO XDFO',
                'hex': '10516ce6f3081c6afd461fe94a8f973a20eb8cae'
            },
            {
                'id': 2,
                'key': 'IHJUVNU5RSB42QEHFI3RWJVPVUDLVKD6',
                'formatted': 'IHJU VNU5 RSB4 2QEH FI3R WJVP VUDL VKD6',
                'hex': '41D34AB69D8C83CD40872A371B26AFAD06BAA87E'
            },
            {
                'id': 3,
                'key': 'TABQ5FWX5FO7ZF6RUGWJJPAY7ISVEQIV',
                'formatted': 'TABQ 5FWX 5FO7 ZF6R UGWJ JPAY 7ISV EQIV',
                'hex': '98030E96D7E95DFC97D1A1AC94BC18FA25524115'
            },
            {
                'id': 4,
                'key': 'ZNYH7AGOK7BCJD3NJXNXVMKACFVM5CAA',
                'formatted': 'ZNYH 7AGO K7BC JD3N JXNX VMKA CFVM 5CAA',
                'hex': 'cb707f80ce57c2248f6d4ddb7ab140116ace8800'
            }
        ]
        
        # 已知的成功密钥作为对比
        self.known_key = {
            'key': 'Z34OJGIQ5SOTPXV2SR2AZI3IXM',
            'formatted': 'Z34O JGIQ 5SOT PXV2 SR2A ZI3I XM',
            'hex': 'cef8e49910ec9d37deba94740ca368bb'
        }
    
    def generate_totp(self, secret, timestamp=None):
        """生成TOTP验证码"""
        try:
            if timestamp is None:
                timestamp = int(time.time())
            
            # TOTP参数
            time_step = 30  # 30秒间隔
            digits = 6      # 6位数字
            
            # 计算时间步数
            time_counter = timestamp // time_step
            
            # 将密钥从Base32解码
            try:
                # 添加填充
                padding_needed = (8 - len(secret) % 8) % 8
                padded_secret = secret + '=' * padding_needed
                key = base64.b32decode(padded_secret)
            except Exception as e:
                return None, f"Base32解码失败: {e}"
            
            # 将时间计数器转换为8字节大端序
            time_bytes = struct.pack('>Q', time_counter)
            
            # 使用HMAC-SHA1计算哈希
            hmac_hash = hmac.new(key, time_bytes, hashlib.sha1).digest()
            
            # 动态截断
            offset = hmac_hash[-1] & 0x0f
            truncated = struct.unpack('>I', hmac_hash[offset:offset + 4])[0]
            truncated &= 0x7fffffff
            
            # 生成指定位数的验证码
            totp_code = truncated % (10 ** digits)
            
            return f"{totp_code:0{digits}d}", None
            
        except Exception as e:
            return None, f"生成失败: {e}"
    
    def test_all_candidates(self):
        """测试所有候选密钥"""
        print("🔑 TOTP密钥验证器")
        print("=" * 60)
        print("🎯 测试4个候选密钥，找出真正的Facebook 2FA密钥")
        print()
        
        current_time = int(time.time())
        current_dt = datetime.fromtimestamp(current_time)
        
        print(f"⏰ 当前时间: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ Unix时间戳: {current_time}")
        print()
        
        # 测试已知密钥作为对比
        print("🔍 首先测试已知的成功密钥 (作为对比):")
        known_code, known_error = self.generate_totp(self.known_key['key'])
        if known_code:
            print(f"   ✅ 已知密钥: {self.known_key['key']}")
            print(f"   🔢 验证码: {known_code}")
        else:
            print(f"   ❌ 已知密钥生成失败: {known_error}")
        print()
        
        # 测试4个候选密钥
        print("🔍 测试4个候选密钥:")
        print("-" * 60)
        
        valid_candidates = []
        
        for candidate in self.candidates:
            print(f"\n{candidate['id']}. 测试密钥: {candidate['key']}")
            print(f"   格式化: {candidate['formatted']}")
            print(f"   原始十六进制: {candidate['hex']}")
            
            # 生成当前时间的验证码
            code, error = self.generate_totp(candidate['key'])
            
            if code:
                print(f"   ✅ 验证码生成成功: {code}")
                
                # 生成前一个时间窗口的验证码
                prev_code, _ = self.generate_totp(candidate['key'], current_time - 30)
                # 生成下一个时间窗口的验证码
                next_code, _ = self.generate_totp(candidate['key'], current_time + 30)
                
                print(f"   📊 时间窗口验证码:")
                print(f"      上一个 (30秒前): {prev_code}")
                print(f"      当前: {code}")
                print(f"      下一个 (30秒后): {next_code}")
                
                # 检查验证码的有效性特征
                validity_score = self.assess_validity(candidate['key'], code)
                print(f"   📈 有效性评分: {validity_score}/100")
                
                candidate_info = {
                    'id': candidate['id'],
                    'key': candidate['key'],
                    'formatted': candidate['formatted'],
                    'current_code': code,
                    'prev_code': prev_code,
                    'next_code': next_code,
                    'validity_score': validity_score
                }
                valid_candidates.append(candidate_info)
                
            else:
                print(f"   ❌ 验证码生成失败: {error}")
        
        # 分析结果
        self.analyze_results(valid_candidates)
        
        return valid_candidates
    
    def assess_validity(self, key, code):
        """评估密钥的有效性"""
        score = 0
        
        # 基础分数
        score += 20
        
        # 密钥长度检查 (32字符是标准长度)
        if len(key) == 32:
            score += 20
        elif len(key) == 26:
            score += 15
        
        # 验证码格式检查 (6位数字)
        if len(code) == 6 and code.isdigit():
            score += 20
        
        # 验证码不应该全是相同数字
        if len(set(code)) > 1:
            score += 15
        
        # 验证码不应该是简单序列
        if code not in ['123456', '654321', '000000', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999']:
            score += 15
        
        # 密钥字符分布检查
        char_counts = {}
        for char in key:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # 字符多样性
        unique_chars = len(char_counts)
        if unique_chars >= 15:
            score += 10
        elif unique_chars >= 10:
            score += 5
        
        return min(100, score)
    
    def analyze_results(self, valid_candidates):
        """分析测试结果"""
        print("\n" + "=" * 60)
        print("📊 测试结果分析")
        print("=" * 60)
        
        if not valid_candidates:
            print("❌ 没有候选密钥能够生成有效的验证码")
            return
        
        # 按有效性评分排序
        sorted_candidates = sorted(valid_candidates, key=lambda x: x['validity_score'], reverse=True)
        
        print(f"✅ {len(valid_candidates)} 个候选密钥成功生成验证码")
        print()
        
        print("🏆 推荐使用顺序 (按有效性评分排序):")
        print("-" * 40)
        
        for i, candidate in enumerate(sorted_candidates, 1):
            print(f"{i}. 候选密钥 #{candidate['id']} (评分: {candidate['validity_score']}/100)")
            print(f"   🔑 密钥: {candidate['key']}")
            print(f"   🔑 格式化: {candidate['formatted']}")
            print(f"   🔢 当前验证码: {candidate['current_code']}")
            
            if i == 1:
                print(f"   ⭐ 最推荐使用这个密钥!")
            
            print()
        
        # 给出使用建议
        best_candidate = sorted_candidates[0]
        print("💡 使用建议:")
        print(f"1. 优先使用候选密钥 #{best_candidate['id']}")
        print(f"2. 在验证器应用中输入: {best_candidate['formatted']}")
        print(f"3. 账户名称: Facebook")
        print(f"4. 当前应该显示验证码: {best_candidate['current_code']}")
        print("5. 在Facebook登录时测试这个验证码")
        print()
        
        print("🔄 实时验证:")
        print("如果当前验证码不工作，请等待30秒后使用新的验证码")
        print(f"下一个验证码应该是: {best_candidate['next_code']}")
    
    def real_time_monitor(self, key_index=1):
        """实时监控指定密钥的验证码"""
        if key_index < 1 or key_index > len(self.candidates):
            print("❌ 无效的密钥索引")
            return
        
        candidate = self.candidates[key_index - 1]
        print(f"🔄 实时监控候选密钥 #{key_index}")
        print(f"🔑 密钥: {candidate['formatted']}")
        print("按 Ctrl+C 停止监控")
        print("-" * 40)
        
        try:
            last_code = None
            while True:
                current_time = int(time.time())
                time_in_window = current_time % 30
                remaining_time = 30 - time_in_window
                
                code, error = self.generate_totp(candidate['key'])
                
                if code and code != last_code:
                    timestamp = datetime.fromtimestamp(current_time).strftime('%H:%M:%S')
                    print(f"[{timestamp}] 验证码: {code} (剩余 {remaining_time} 秒)")
                    last_code = code
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n监控已停止")

def main():
    """主函数"""
    validator = TOTPValidator()
    
    print("选择操作:")
    print("1. 测试所有候选密钥")
    print("2. 实时监控指定密钥")
    
    choice = input("请输入选择 (1-2): ").strip()
    
    if choice == "1":
        valid_candidates = validator.test_all_candidates()
    elif choice == "2":
        key_index = input("请输入要监控的密钥编号 (1-4): ").strip()
        try:
            key_index = int(key_index)
            validator.real_time_monitor(key_index)
        except ValueError:
            print("❌ 请输入有效的数字")
    else:
        print("❌ 无效选择，默认测试所有密钥")
        validator.test_all_candidates()

if __name__ == "__main__":
    main()
