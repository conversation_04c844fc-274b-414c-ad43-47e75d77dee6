#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十六进制转换器
将找到的十六进制字符串转换为Base32 TOTP密钥
"""

import base64
import binascii

def hex_to_base32(hex_string):
    """将十六进制转换为Base32格式"""
    try:
        # 清理十六进制字符串
        hex_clean = hex_string.replace(' ', '').lower()
        
        # 转换为字节
        bytes_data = binascii.unhexlify(hex_clean)
        
        # 转换为Base32
        base32_string = base64.b32encode(bytes_data).decode('ascii')
        
        # 移除填充字符
        base32_clean = base32_string.rstrip('=')
        
        # 格式化为标准TOTP格式 (每4个字符一组)
        formatted = ' '.join([base32_clean[i:i+4] for i in range(0, len(base32_clean), 4)])
        
        return {
            'clean': base32_clean,
            'formatted': formatted,
            'length': len(base32_clean)
        }
        
    except Exception as e:
        return None

def main():
    """主函数"""
    print("🔑 十六进制转Base32转换器")
    print("=" * 50)
    
    # 从模拟器1中找到的十六进制字符串
    hex_keys = [
        "10516ce6f3081c6afd461fe94a8f973a20eb8cae",  # 40字符
        "41D34AB69D8C83CD40872A371B26AFAD06BAA87E",  # 40字符
        "98030E96D7E95DFC97D1A1AC94BC18FA25524115",  # 40字符
        "cb707f80ce57c2248f6d4ddb7ab140116ace8800",  # 40字符
        "10516ce6f3081c6afd461fe94a8f973a20eb8caec212200c1a30",  # 53字符
    ]
    
    print(f"📝 找到 {len(hex_keys)} 个十六进制候选")
    
    valid_keys = []
    
    for i, hex_key in enumerate(hex_keys, 1):
        print(f"\n{i}. 原始十六进制: {hex_key}")
        print(f"   长度: {len(hex_key)} 字符")
        
        # 转换为Base32
        result = hex_to_base32(hex_key)
        
        if result:
            print(f"   ✅ 转换成功!")
            print(f"   🔑 Base32密钥: {result['clean']}")
            print(f"   🔑 格式化: {result['formatted']}")
            print(f"   📏 Base32长度: {result['length']} 字符")
            
            # 检查是否为标准TOTP长度
            if result['length'] in [16, 20, 24, 26, 32]:
                print(f"   ⭐ 标准TOTP长度!")
                valid_keys.append({
                    'original_hex': hex_key,
                    'base32_key': result['clean'],
                    'formatted': result['formatted'],
                    'length': result['length']
                })
            else:
                print(f"   ⚠️ 非标准长度")
        else:
            print(f"   ❌ 转换失败")
    
    if valid_keys:
        print(f"\n🎉 找到 {len(valid_keys)} 个有效的TOTP候选密钥:")
        print("=" * 50)
        
        for i, key in enumerate(valid_keys, 1):
            print(f"{i}. {key['base32_key']}")
            print(f"   格式化: {key['formatted']}")
            print(f"   长度: {key['length']} 字符")
            print(f"   原始十六进制: {key['original_hex']}")
            print()
        
        print("📱 使用说明:")
        print("1. 在验证器应用中点击 '+' 添加账户")
        print("2. 选择 '手动输入' 或 '输入密钥'")
        print("3. 输入上面的Base32密钥")
        print("4. 账户名称: Facebook")
        print("5. 保存后开始生成6位验证码")
        
    else:
        print("\n❌ 未找到有效的TOTP密钥")

if __name__ == "__main__":
    main()
