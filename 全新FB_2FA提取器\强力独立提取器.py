#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力独立Facebook 2FA提取器
直接提取，不依赖用户ID验证
彻底解决数据共享问题
"""

import os
import json
import subprocess
import base64
import binascii
import hashlib
from datetime import datetime

class PowerfulIndependentExtractor:
    def __init__(self):
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.results = {}
        
        print(f"💪 强力独立会话ID: {self.session_id}")
        print(f"⏰ 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def execute_adb(self, device_id, command):
        """执行ADB命令"""
        try:
            full_cmd = f'"{self.adb_path}" -s {device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
        except Exception as e:
            return None
    
    def get_devices(self):
        """获取设备列表"""
        try:
            result = subprocess.run(f'"{self.adb_path}" devices', shell=True, capture_output=True, text=True)
            
            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                for line in lines:
                    if line.strip() and 'device' in line:
                        device_id = line.split()[0]
                        devices.append(device_id)
            
            return devices
        except:
            return []
    
    def force_extract_user_info(self, device_id):
        """强力提取用户信息"""
        print(f"  💪 强力提取设备 {device_id} 的用户信息...")
        
        # 强制重启Facebook应用
        self.execute_adb(device_id, "am force-stop com.facebook.katana")
        import time
        time.sleep(2)
        self.execute_adb(device_id, "am start -n com.facebook.katana/.LoginActivity")
        time.sleep(3)
        
        user_info = {
            'user_id': None,
            'profile_url': None,
            'device_serial': None
        }
        
        # 获取设备序列号
        serial = self.execute_adb(device_id, "getprop ro.serialno")
        user_info['device_serial'] = serial
        print(f"    📱 设备序列号: {serial}")
        
        # 多种方法强力提取用户ID
        user_id_methods = [
            # 方法1: 从认证文件
            "cat /data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml 2>/dev/null | grep -o '[0-9]\\{10,15\\}' | head -1",
            
            # 方法2: 从数据库
            "strings /data/data/com.facebook.katana/databases/prefs_db 2>/dev/null | grep -o '[0-9]\\{10,15\\}' | head -1",
            
            # 方法3: 从所有XML文件
            "find /data/data/com.facebook.katana/shared_prefs/ -name '*.xml' -exec cat {} \\; 2>/dev/null | grep -o '[0-9]\\{10,15\\}' | head -1",
            
            # 方法4: 从profile URL
            "find /data/data/com.facebook.katana/ -type f -exec grep -o 'facebook.com/profile.php?id=[0-9]\\{10,15\\}' {} \\; 2>/dev/null | head -1 | grep -o '[0-9]\\{10,15\\}'",
            
            # 方法5: 从所有文件暴力搜索
            "find /data/data/com.facebook.katana/ -type f -exec strings {} \\; 2>/dev/null | grep -o '[0-9]\\{10,15\\}' | head -1"
        ]
        
        for i, method in enumerate(user_id_methods, 1):
            print(f"    🔍 方法{i}: 尝试提取用户ID...")
            result = self.execute_adb(device_id, method)
            if result and result.isdigit() and len(result) >= 10:
                user_info['user_id'] = result
                print(f"    ✅ 方法{i}成功: {result}")
                break
            else:
                print(f"    ❌ 方法{i}失败")
        
        # 提取profile URL
        profile_url = self.execute_adb(device_id, "find /data/data/com.facebook.katana/ -type f -exec grep -o 'https://www.facebook.com/profile.php?id=[0-9]\\{10,15\\}' {} \\; 2>/dev/null | head -1")
        if profile_url:
            user_info['profile_url'] = profile_url
            print(f"    🔗 Profile URL: {profile_url}")
        
        return user_info
    
    def force_extract_totp_keys(self, device_id, user_info):
        """强力提取TOTP密钥"""
        print(f"  🔑 强力提取TOTP密钥...")
        
        found_keys = []
        
        # 确保应用处于活跃状态
        self.execute_adb(device_id, "am start -n com.facebook.katana/.LoginActivity")
        import time
        time.sleep(2)
        
        # 方法1: 暴力搜索所有十六进制
        print("    💪 方法1: 暴力搜索十六进制...")
        hex_commands = [
            # 从prefs_db
            "strings /data/data/com.facebook.katana/databases/prefs_db 2>/dev/null | grep -o '[a-fA-F0-9]\\{40\\}'",
            "strings /data/data/com.facebook.katana/databases/prefs_db 2>/dev/null | grep -o '[a-fA-F0-9]\\{52\\}'",
            
            # 从所有数据库
            "find /data/data/com.facebook.katana/databases/ -name '*.db' -exec strings {} \\; 2>/dev/null | grep -o '[a-fA-F0-9]\\{40,52\\}'",
            
            # 从所有文件
            "find /data/data/com.facebook.katana/ -type f -exec strings {} \\; 2>/dev/null | grep -o '[a-fA-F0-9]\\{40,52\\}'"
        ]
        
        all_hex_keys = set()
        for cmd in hex_commands:
            result = self.execute_adb(device_id, cmd)
            if result:
                for line in result.split('\n'):
                    if line.strip() and len(line.strip()) in [40, 52]:
                        all_hex_keys.add(line.strip())
        
        print(f"      📊 发现 {len(all_hex_keys)} 个唯一十六进制候选")
        
        # 转换十六进制为Base32
        for hex_key in all_hex_keys:
            base32_key = self.hex_to_base32(hex_key)
            if base32_key and self.is_valid_totp(base32_key):
                key_info = {
                    'device_id': device_id,
                    'device_serial': user_info['device_serial'],
                    'user_id': user_info['user_id'],
                    'profile_url': user_info['profile_url'],
                    'base32_key': base32_key,
                    'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                    'original_hex': hex_key,
                    'length': len(base32_key),
                    'method': 'hex_conversion',
                    'session_id': self.session_id,
                    'extraction_time': datetime.now().isoformat()
                }
                found_keys.append(key_info)
                print(f"      ✅ 十六进制转换: {hex_key} → {base32_key}")
        
        # 方法2: 直接搜索Base32
        print("    💪 方法2: 直接搜索Base32...")
        base32_commands = [
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{16\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{20\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null"
        ]
        
        all_base32_keys = set()
        for cmd in base32_commands:
            result = self.execute_adb(device_id, cmd)
            if result:
                for line in result.split('\n'):
                    if line.strip() and len(line.strip()) >= 16:
                        all_base32_keys.add(line.strip())
        
        print(f"      📊 发现 {len(all_base32_keys)} 个Base32候选")
        
        # 验证Base32密钥
        for base32_key in all_base32_keys:
            if self.is_valid_totp(base32_key):
                # 检查是否已存在
                if not any(k['base32_key'] == base32_key for k in found_keys):
                    key_info = {
                        'device_id': device_id,
                        'device_serial': user_info['device_serial'],
                        'user_id': user_info['user_id'],
                        'profile_url': user_info['profile_url'],
                        'base32_key': base32_key,
                        'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                        'length': len(base32_key),
                        'method': 'direct_base32',
                        'session_id': self.session_id,
                        'extraction_time': datetime.now().isoformat()
                    }
                    found_keys.append(key_info)
                    print(f"      ✅ 直接Base32: {base32_key}")
        
        print(f"    📊 总共提取到 {len(found_keys)} 个有效TOTP密钥")
        return found_keys
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            bytes_data = binascii.unhexlify(hex_string)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def is_valid_totp(self, key):
        """验证TOTP密钥"""
        try:
            if len(key) < 16 or len(key) > 32:
                return False
            
            # 字符检查
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # Base32解码测试
            padding = (8 - len(key) % 8) % 8
            padded = key + '=' * padding
            base64.b32decode(padded)
            
            # 排除明显的非密钥
            exclude = ['FACEBOOK', 'GOOGLE', 'ANDROID', 'AAAAAAA', 'BBBBBBB', 'CCCCCCC']
            for pattern in exclude:
                if pattern in key.upper():
                    return False
            
            # 检查字符分布
            char_counts = {}
            for char in key:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            # 如果某个字符出现过多，可能不是真正的密钥
            max_char_ratio = max(char_counts.values()) / len(key)
            if max_char_ratio > 0.5:
                return False
            
            return True
        except:
            return False
    
    def run_extraction(self):
        """运行强力提取"""
        print("💪 强力独立Facebook 2FA提取器")
        print("=" * 50)
        
        # 获取设备
        devices = self.get_devices()
        
        if not devices:
            print("❌ 未发现设备")
            return
        
        print(f"📱 发现 {len(devices)} 个设备: {devices}")
        
        # 强力分析每个设备
        for device_id in devices:
            print(f"\n💪 强力分析设备: {device_id}")
            
            # 检查Facebook
            fb_check = self.execute_adb(device_id, "pm list packages | grep facebook")
            if not fb_check or "com.facebook.katana" not in fb_check:
                print("  ❌ Facebook未安装")
                continue
            
            print("  ✅ Facebook已安装")
            
            # 检查数据访问
            data_check = self.execute_adb(device_id, "ls /data/data/com.facebook.katana/")
            if not data_check:
                print("  ❌ 无法访问Facebook数据")
                continue
            
            print("  ✅ Facebook数据可访问")
            
            # 强力提取用户信息
            user_info = self.force_extract_user_info(device_id)
            
            # 强力提取TOTP密钥
            totp_keys = self.force_extract_totp_keys(device_id, user_info)
            
            # 保存结果
            self.results[device_id] = {
                'user_info': user_info,
                'totp_keys': totp_keys,
                'session_id': self.session_id,
                'extraction_time': datetime.now().isoformat()
            }
        
        # 显示结果
        self.display_results()
        
        # 保存到文件
        self.save_results()
    
    def display_results(self):
        """显示结果"""
        print("\n" + "=" * 60)
        print("📊 强力提取结果")
        print("=" * 60)
        
        for device_id, data in self.results.items():
            user_info = data['user_info']
            totp_keys = data['totp_keys']
            
            print(f"\n📱 设备: {device_id}")
            print(f"📱 序列号: {user_info['device_serial']}")
            print(f"👤 用户ID: {user_info['user_id'] or '未知'}")
            if user_info['profile_url']:
                print(f"🔗 Profile URL: {user_info['profile_url']}")
            print(f"🆔 会话ID: {data['session_id']}")
            print(f"⏰ 提取时间: {data['extraction_time']}")
            
            if totp_keys:
                print(f"🔑 TOTP密钥 ({len(totp_keys)}个):")
                for i, key in enumerate(totp_keys, 1):
                    print(f"  {i}. {key['base32_key']}")
                    print(f"     格式化: {key['formatted']}")
                    print(f"     长度: {key['length']} 字符")
                    print(f"     提取方法: {key['method']}")
                    if 'original_hex' in key:
                        print(f"     原始十六进制: {key['original_hex']}")
                    print()
            else:
                print("❌ 未找到TOTP密钥")
        
        # 强力对比分析
        if len(self.results) > 1:
            print("\n💪 强力对比分析:")
            print("-" * 40)
            
            # 收集所有用户ID
            user_ids = []
            device_serials = []
            all_keys = []
            
            for device_id, data in self.results.items():
                user_id = data['user_info']['user_id']
                serial = data['user_info']['device_serial']
                
                user_ids.append(user_id)
                device_serials.append(serial)
                
                for key in data['totp_keys']:
                    all_keys.append((device_id, key['base32_key']))
            
            # 分析用户ID
            unique_user_ids = [uid for uid in set(user_ids) if uid]
            if len(unique_user_ids) == 0:
                print("⚠️ 警告: 无法获取任何用户ID")
            elif len(unique_user_ids) == 1:
                print(f"⚠️ 警告: 所有设备使用相同的用户ID: {unique_user_ids[0]}")
                print("   这可能表示数据共享或克隆问题")
            else:
                print(f"✅ 发现 {len(unique_user_ids)} 个不同的用户ID:")
                for uid in unique_user_ids:
                    print(f"   - {uid}")
            
            # 分析设备序列号
            unique_serials = [s for s in set(device_serials) if s]
            if len(unique_serials) == 1:
                print(f"⚠️ 警告: 所有设备有相同的序列号: {unique_serials[0]}")
                print("   这表示设备可能是克隆的")
            else:
                print(f"✅ 发现 {len(unique_serials)} 个不同的设备序列号")
            
            # 分析TOTP密钥
            key_values = [key[1] for key in all_keys]
            unique_keys = set(key_values)
            
            if len(unique_keys) < len(key_values):
                print("⚠️ 警告: 发现重复的TOTP密钥!")
                print("   不同用户不应该有相同的TOTP密钥")
                
                # 显示重复情况
                key_count = {}
                for device_id, key in all_keys:
                    if key not in key_count:
                        key_count[key] = []
                    key_count[key].append(device_id)
                
                for key, devices in key_count.items():
                    if len(devices) > 1:
                        print(f"   重复密钥 {key} 出现在设备: {', '.join(devices)}")
            else:
                print("✅ 所有TOTP密钥都是唯一的")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"强力提取结果_{self.session_id}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到: {filename}")

def main():
    extractor = PowerfulIndependentExtractor()
    extractor.run_extraction()

if __name__ == "__main__":
    main()
