#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android自动化环境安装和设备连接脚本
支持UIAutomator2和WEditor浏览器操作界面
"""

import os
import sys
import subprocess
import platform
import requests
import zipfile
import shutil
from pathlib import Path

class AndroidAutomationSetup:
    def __init__(self):
        self.system = platform.system().lower()
        self.current_dir = Path.cwd()
        self.tools_dir = self.current_dir / "android_tools"
        
    def check_python(self):
        """检查Python环境"""
        print("🔍 检查Python环境...")
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
            print("❌ 需要Python 3.6或更高版本")
            return False
        print(f"✅ Python版本: {sys.version}")
        return True
    
    def install_adb(self):
        """安装ADB工具"""
        print("🔧 安装ADB工具...")
        
        # 创建工具目录
        self.tools_dir.mkdir(exist_ok=True)
        
        if self.system == "windows":
            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
            adb_file = "platform-tools-windows.zip"
        elif self.system == "darwin":  # macOS
            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-darwin.zip"
            adb_file = "platform-tools-darwin.zip"
        else:  # Linux
            adb_url = "https://dl.google.com/android/repository/platform-tools-latest-linux.zip"
            adb_file = "platform-tools-linux.zip"
        
        adb_path = self.tools_dir / adb_file
        
        try:
            print(f"📥 下载ADB工具: {adb_url}")
            response = requests.get(adb_url, stream=True)
            response.raise_for_status()
            
            with open(adb_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("📦 解压ADB工具...")
            with zipfile.ZipFile(adb_path, 'r') as zip_ref:
                zip_ref.extractall(self.tools_dir)
            
            # 添加到PATH
            platform_tools_path = self.tools_dir / "platform-tools"
            if self.system == "windows":
                adb_exe = platform_tools_path / "adb.exe"
            else:
                adb_exe = platform_tools_path / "adb"
                # 给执行权限
                os.chmod(adb_exe, 0o755)
            
            print(f"✅ ADB安装完成: {adb_exe}")
            return str(platform_tools_path)
            
        except Exception as e:
            print(f"❌ ADB安装失败: {e}")
            return None
    
    def install_python_packages(self):
        """安装Python包"""
        print("📦 安装Python包...")
        packages = [
            "uiautomator2",
            "weditor",
            "requests",
            "pillow"
        ]
        
        for package in packages:
            try:
                print(f"安装 {package}...")
                subprocess.run([sys.executable, "-m", "pip", "install", "-U", package], 
                             check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")
                return False
        return True
    
    def check_devices(self, adb_path=None):
        """检查连接的设备"""
        print("🔍 检查连接的设备...")
        
        adb_cmd = "adb"
        if adb_path:
            if self.system == "windows":
                adb_cmd = str(Path(adb_path) / "adb.exe")
            else:
                adb_cmd = str(Path(adb_path) / "adb")
        
        try:
            result = subprocess.run([adb_cmd, "devices"], 
                                  capture_output=True, text=True, check=True)
            
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            devices = []
            
            for line in lines:
                if line.strip() and 'device' in line:
                    serial = line.split('\t')[0]
                    devices.append(serial)
            
            if devices:
                print(f"✅ 发现 {len(devices)} 个设备:")
                for i, device in enumerate(devices, 1):
                    print(f"  {i}. {device}")
                return devices
            else:
                print("❌ 未发现任何设备")
                print("请确保:")
                print("  1. 设备已开启USB调试")
                print("  2. 设备已连接到电脑")
                print("  3. 已允许USB调试权限")
                return []
                
        except subprocess.CalledProcessError as e:
            print(f"❌ 检查设备失败: {e}")
            return []
    
    def init_uiautomator2(self, device_serial=None):
        """初始化UIAutomator2"""
        print("🚀 初始化UIAutomator2...")
        
        try:
            cmd = [sys.executable, "-m", "uiautomator2", "init"]
            if device_serial:
                cmd.extend(["--serial", device_serial])
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ UIAutomator2初始化成功")
            print(result.stdout)
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ UIAutomator2初始化失败: {e}")
            print(e.stderr)
            return False
    
    def start_weditor(self):
        """启动WEditor浏览器界面"""
        print("🌐 启动WEditor浏览器界面...")
        
        try:
            # 启动weditor
            subprocess.Popen([sys.executable, "-m", "weditor"])
            print("✅ WEditor已启动")
            print("🌐 浏览器界面将自动打开: http://localhost:17310")
            print("📱 在浏览器中可以:")
            print("  - 查看设备屏幕")
            print("  - 点击元素")
            print("  - 查看元素属性")
            print("  - 生成自动化代码")
            return True
            
        except Exception as e:
            print(f"❌ WEditor启动失败: {e}")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UIAutomator2测试脚本示例
"""

import uiautomator2 as u2
import time

def connect_device(serial=None):
    """连接设备"""
    try:
        if serial:
            d = u2.connect(serial)
        else:
            d = u2.connect()  # 自动连接第一个设备
        
        print(f"设备信息: {d.info}")
        return d
    except Exception as e:
        print(f"连接设备失败: {e}")
        return None

def test_basic_operations(d):
    """基本操作测试"""
    print("开始基本操作测试...")
    
    # 获取屏幕截图
    d.screenshot("screenshot.png")
    print("截图已保存: screenshot.png")
    
    # 获取设备信息
    print(f"屏幕尺寸: {d.window_size()}")
    print(f"设备方向: {d.orientation}")
    
    # 点击屏幕中心
    width, height = d.window_size()
    d.click(width//2, height//2)
    time.sleep(1)
    
    print("基本操作测试完成")

if __name__ == "__main__":
    # 连接设备
    device = connect_device()
    
    if device:
        # 运行测试
        test_basic_operations(device)
    else:
        print("无法连接设备，请检查连接")
'''
        
        with open("test_uiautomator2.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        print("✅ 测试脚本已创建: test_uiautomator2.py")
    
    def run_setup(self):
        """运行完整安装流程"""
        print("🚀 开始Android自动化环境安装...")
        print("=" * 50)
        
        # 1. 检查Python
        if not self.check_python():
            return False
        
        # 2. 安装ADB
        adb_path = self.install_adb()
        
        # 3. 安装Python包
        if not self.install_python_packages():
            return False
        
        # 4. 检查设备
        devices = self.check_devices(adb_path)
        
        if devices:
            # 5. 初始化UIAutomator2
            device_serial = devices[0] if len(devices) == 1 else None
            if len(devices) > 1:
                print("发现多个设备，请选择:")
                for i, device in enumerate(devices, 1):
                    print(f"  {i}. {device}")
                try:
                    choice = int(input("请输入设备编号: ")) - 1
                    device_serial = devices[choice]
                except (ValueError, IndexError):
                    print("无效选择，使用第一个设备")
                    device_serial = devices[0]
            
            self.init_uiautomator2(device_serial)
            
            # 6. 创建测试脚本
            self.create_test_script()
            
            # 7. 启动WEditor
            self.start_weditor()
            
            print("\n" + "=" * 50)
            print("🎉 安装完成！")
            print("\n📋 使用说明:")
            print("1. 浏览器界面: http://localhost:17310")
            print("2. 运行测试: python test_uiautomator2.py")
            print("3. 查看设备: adb devices")
            
            return True
        else:
            print("\n⚠️  未检测到设备，请:")
            print("1. 连接Android设备")
            print("2. 开启USB调试")
            print("3. 允许USB调试权限")
            print("4. 重新运行此脚本")
            return False

if __name__ == "__main__":
    setup = AndroidAutomationSetup()
    setup.run_setup()
