#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解决方案 - Facebook 2FA提取器
专门处理动态数据和会话状态问题
"""

import os
import json
import subprocess
import base64
import binascii
import hashlib
import time
from datetime import datetime

class FinalSolutionExtractor:
    def __init__(self):
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.results = {}
        
        print(f"🎯 最终解决方案会话ID: {self.session_id}")
    
    def execute_adb(self, device_id, command):
        """执行ADB命令"""
        try:
            full_cmd = f'"{self.adb_path}" -s {device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
        except:
            return None
    
    def get_devices(self):
        """获取设备列表"""
        try:
            result = subprocess.run(f'"{self.adb_path}" devices', shell=True, capture_output=True, text=True)
            
            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                for line in lines:
                    if line.strip() and 'device' in line:
                        device_id = line.split()[0]
                        devices.append(device_id)
            
            return devices
        except:
            return []
    
    def trigger_facebook_2fa_state(self, device_id):
        """触发Facebook 2FA状态"""
        print(f"  🔄 触发Facebook 2FA状态...")
        
        # 步骤1: 完全停止Facebook
        self.execute_adb(device_id, "am force-stop com.facebook.katana")
        time.sleep(2)
        
        # 步骤2: 清理临时数据但保留认证信息
        self.execute_adb(device_id, "rm -rf /data/data/com.facebook.katana/cache/*")
        time.sleep(1)
        
        # 步骤3: 启动Facebook主界面
        self.execute_adb(device_id, "am start -n com.facebook.katana/.LoginActivity")
        time.sleep(3)
        
        # 步骤4: 尝试触发设置页面（可能包含2FA信息）
        self.execute_adb(device_id, "am start -n com.facebook.katana/.activity.FbMainTabActivity")
        time.sleep(2)
        
        # 步骤5: 模拟用户交互，可能触发2FA数据加载
        self.execute_adb(device_id, "input tap 500 500")  # 模拟点击
        time.sleep(1)
        
        print(f"    ✅ Facebook状态已触发")
    
    def extract_with_state_cycling(self, device_id):
        """通过状态循环提取TOTP密钥"""
        print(f"  🔄 状态循环提取...")
        
        all_found_keys = []
        
        # 循环3次，每次都触发不同的应用状态
        for cycle in range(3):
            print(f"    🔄 循环 {cycle + 1}/3...")
            
            # 触发Facebook状态
            self.trigger_facebook_2fa_state(device_id)
            
            # 立即提取数据
            cycle_keys = self.extract_immediate_data(device_id, cycle)
            all_found_keys.extend(cycle_keys)
            
            # 短暂等待
            time.sleep(2)
        
        # 去重
        unique_keys = self.deduplicate_keys(all_found_keys)
        print(f"    📊 循环提取完成，找到 {len(unique_keys)} 个唯一密钥")
        
        return unique_keys
    
    def extract_immediate_data(self, device_id, cycle):
        """立即提取数据"""
        found_keys = []
        
        # 快速提取十六进制数据
        hex_commands = [
            "strings /data/data/com.facebook.katana/databases/prefs_db 2>/dev/null | grep -o '[a-fA-F0-9]\\{40,52\\}' | head -10",
            "find /data/data/com.facebook.katana/databases/ -name '*.db' -exec strings {} \\; 2>/dev/null | grep -o '[a-fA-F0-9]\\{40,52\\}' | head -5"
        ]
        
        for cmd in hex_commands:
            result = self.execute_adb(device_id, cmd)
            if result:
                for line in result.split('\n'):
                    if line.strip() and len(line.strip()) in [40, 52]:
                        base32_key = self.hex_to_base32(line.strip())
                        if base32_key and self.is_valid_totp(base32_key):
                            key_info = {
                                'device_id': device_id,
                                'base32_key': base32_key,
                                'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                                'original_hex': line.strip(),
                                'length': len(base32_key),
                                'extraction_cycle': cycle + 1,
                                'extraction_time': datetime.now().isoformat()
                            }
                            found_keys.append(key_info)
        
        return found_keys
    
    def manual_trigger_extraction(self, device_id):
        """手动触发提取（需要用户配合）"""
        print(f"  👆 手动触发提取...")
        print(f"    请在设备 {device_id} 上执行以下操作：")
        print(f"    1. 打开Facebook应用")
        print(f"    2. 进入设置 → 安全性和登录")
        print(f"    3. 点击双重验证")
        print(f"    4. 查看验证器应用设置")
        
        input("    按回车键继续提取...")
        
        # 在用户操作后立即提取
        return self.extract_immediate_data(device_id, 999)  # 使用特殊循环号
    
    def comprehensive_extraction(self, device_id):
        """综合提取方法"""
        print(f"  🎯 综合提取方法...")
        
        all_keys = []
        
        # 方法1: 状态循环提取
        print(f"    方法1: 状态循环提取")
        cycle_keys = self.extract_with_state_cycling(device_id)
        all_keys.extend(cycle_keys)
        
        # 方法2: 深度文件搜索
        print(f"    方法2: 深度文件搜索")
        deep_keys = self.deep_file_search(device_id)
        all_keys.extend(deep_keys)
        
        # 方法3: 内存转储分析
        print(f"    方法3: 内存转储分析")
        memory_keys = self.memory_dump_analysis(device_id)
        all_keys.extend(memory_keys)
        
        # 如果前面的方法都没找到，尝试手动触发
        if not all_keys:
            print(f"    ⚠️ 自动方法未找到密钥，尝试手动触发...")
            manual_keys = self.manual_trigger_extraction(device_id)
            all_keys.extend(manual_keys)
        
        # 最终去重
        unique_keys = self.deduplicate_keys(all_keys)
        return unique_keys
    
    def deep_file_search(self, device_id):
        """深度文件搜索"""
        found_keys = []
        
        # 搜索所有可能包含密钥的文件
        search_commands = [
            # 搜索所有XML文件
            "find /data/data/com.facebook.katana/shared_prefs/ -name '*.xml' -exec cat {} \\; 2>/dev/null | grep -o '[A-Z2-7]\\{16,32\\}'",
            
            # 搜索所有数据库文件
            "find /data/data/com.facebook.katana/databases/ -name '*.db' -exec strings {} \\; 2>/dev/null | grep -o '[A-Z2-7]\\{16,32\\}'",
            
            # 搜索缓存文件
            "find /data/data/com.facebook.katana/cache/ -type f -exec strings {} \\; 2>/dev/null | grep -o '[A-Z2-7]\\{16,32\\}'",
            
            # 搜索所有文件
            "find /data/data/com.facebook.katana/ -type f -exec strings {} \\; 2>/dev/null | grep -o '[A-Z2-7]\\{16,32\\}'"
        ]
        
        for cmd in search_commands:
            result = self.execute_adb(device_id, cmd)
            if result:
                for line in result.split('\n'):
                    if line.strip() and self.is_valid_totp(line.strip()):
                        key_info = {
                            'device_id': device_id,
                            'base32_key': line.strip(),
                            'formatted': ' '.join([line.strip()[i:i+4] for i in range(0, len(line.strip()), 4)]),
                            'length': len(line.strip()),
                            'method': 'deep_file_search',
                            'extraction_time': datetime.now().isoformat()
                        }
                        found_keys.append(key_info)
        
        return found_keys
    
    def memory_dump_analysis(self, device_id):
        """内存转储分析"""
        found_keys = []
        
        # 获取Facebook进程信息
        ps_result = self.execute_adb(device_id, "ps | grep facebook")
        if ps_result:
            try:
                pid = ps_result.split()[1]
                if pid.isdigit():
                    # 尝试从进程内存中提取信息
                    # 注意：这在某些系统上可能受限
                    memory_cmd = f"cat /proc/{pid}/cmdline 2>/dev/null"
                    self.execute_adb(device_id, memory_cmd)
                    
                    # 这里可以添加更多内存分析逻辑
                    # 由于安全限制，简化处理
            except:
                pass
        
        return found_keys
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            bytes_data = binascii.unhexlify(hex_string)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def is_valid_totp(self, key):
        """验证TOTP密钥"""
        try:
            if len(key) < 16 or len(key) > 32:
                return False
            
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            padding = (8 - len(key) % 8) % 8
            padded = key + '=' * padding
            base64.b32decode(padded)
            
            exclude = ['FACEBOOK', 'GOOGLE', 'ANDROID', 'AAAAAAA', 'BBBBBBB']
            for pattern in exclude:
                if pattern in key.upper():
                    return False
            
            return True
        except:
            return False
    
    def deduplicate_keys(self, keys):
        """去重密钥"""
        seen = set()
        unique_keys = []
        
        for key in keys:
            key_value = key['base32_key']
            if key_value not in seen:
                seen.add(key_value)
                unique_keys.append(key)
        
        return unique_keys
    
    def run_final_extraction(self):
        """运行最终提取"""
        print("🎯 最终解决方案 - Facebook 2FA提取器")
        print("=" * 50)
        
        devices = self.get_devices()
        
        if not devices:
            print("❌ 未发现设备")
            return
        
        print(f"📱 发现 {len(devices)} 个设备: {devices}")
        
        for device_id in devices:
            print(f"\n🎯 最终提取设备: {device_id}")
            
            # 检查Facebook
            fb_check = self.execute_adb(device_id, "pm list packages | grep facebook")
            if not fb_check or "com.facebook.katana" not in fb_check:
                print("  ❌ Facebook未安装")
                continue
            
            # 检查数据访问
            data_check = self.execute_adb(device_id, "ls /data/data/com.facebook.katana/")
            if not data_check:
                print("  ❌ 无法访问Facebook数据")
                continue
            
            print("  ✅ 开始综合提取...")
            
            # 综合提取
            totp_keys = self.comprehensive_extraction(device_id)
            
            # 获取设备信息
            serial = self.execute_adb(device_id, "getprop ro.serialno")
            
            # 保存结果
            self.results[device_id] = {
                'device_serial': serial,
                'totp_keys': totp_keys,
                'session_id': self.session_id,
                'extraction_time': datetime.now().isoformat()
            }
        
        # 显示结果
        self.display_final_results()
        
        # 保存结果
        self.save_final_results()
    
    def display_final_results(self):
        """显示最终结果"""
        print("\n" + "=" * 60)
        print("🎯 最终提取结果")
        print("=" * 60)
        
        total_keys = 0
        
        for device_id, data in self.results.items():
            totp_keys = data['totp_keys']
            total_keys += len(totp_keys)
            
            print(f"\n📱 设备: {device_id}")
            print(f"📱 序列号: {data['device_serial']}")
            print(f"⏰ 提取时间: {data['extraction_time']}")
            
            if totp_keys:
                print(f"🔑 TOTP密钥 ({len(totp_keys)}个):")
                for i, key in enumerate(totp_keys, 1):
                    print(f"  {i}. {key['base32_key']}")
                    print(f"     格式化: {key['formatted']}")
                    print(f"     长度: {key['length']} 字符")
                    if 'original_hex' in key:
                        print(f"     原始十六进制: {key['original_hex']}")
                    if 'extraction_cycle' in key:
                        print(f"     提取循环: {key['extraction_cycle']}")
                    print()
            else:
                print("❌ 未找到TOTP密钥")
        
        print(f"\n📊 总结: 在 {len(self.results)} 个设备中找到 {total_keys} 个TOTP密钥")
        
        if total_keys == 0:
            print("\n💡 如果未找到密钥，可能的原因：")
            print("1. Facebook账号未启用2FA")
            print("2. 使用的是SMS 2FA而不是验证器应用")
            print("3. TOTP密钥存储在其他位置")
            print("4. 需要特定的应用状态才能访问密钥")
    
    def save_final_results(self):
        """保存最终结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"最终提取结果_{self.session_id}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 最终结果已保存到: {filename}")

def main():
    extractor = FinalSolutionExtractor()
    extractor.run_final_extraction()

if __name__ == "__main__":
    main()
