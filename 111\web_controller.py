#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版浏览器操作界面
替代weditor，提供基本的设备控制功能
"""

import uiautomator2 as u2
import base64
import json
import time
from flask import Flask, render_template_string, request, jsonify
import threading
import webbrowser
from io import BytesIO

app = Flask(__name__)

class DeviceController:
    def __init__(self):
        self.devices = {}
        self.current_device = None
        self.discover_devices()
    
    def discover_devices(self):
        """发现可用设备"""
        import subprocess
        try:
            result = subprocess.run(['./android_tools/platform-tools/adb.exe', 'devices'], 
                                  capture_output=True, text=True, check=True)
            lines = result.stdout.strip().split('\n')[1:]
            
            for line in lines:
                if 'device' in line and '\t' in line:
                    serial = line.split('\t')[0]
                    try:
                        device = u2.connect(serial)
                        self.devices[serial] = device
                        print(f"✅ 连接设备: {serial}")
                        if not self.current_device:
                            self.current_device = device
                    except Exception as e:
                        print(f"❌ 连接设备 {serial} 失败: {e}")
        except Exception as e:
            print(f"发现设备失败: {e}")
    


    def get_screenshot(self):
        """获取屏幕截图"""
        if not self.current_device:
            return None

        try:
            screenshot = self.current_device.screenshot(format='pillow')
            buffer = BytesIO()
            screenshot.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return img_str
        except Exception as e:
            print(f"截图失败: {e}")
            return None
    
    def click(self, x, y):
        """点击坐标"""
        if self.current_device:
            try:
                self.current_device.click(x, y)
                return True
            except Exception as e:
                print(f"点击失败: {e}")
        return False

    def get_device_info(self):
        """获取设备信息"""
        if self.current_device:
            try:
                info = self.current_device.info
                return {
                    'display': info.get('display', {}),
                    'product': info.get('product', 'Unknown'),
                    'model': info.get('model', 'Unknown'),
                    'version': info.get('version', 'Unknown')
                }
            except:
                return {}
        return {}

    def get_element_at_position(self, x, y):
        """获取指定坐标处的UI元素信息"""
        if not self.current_device:
            return None

        try:
            # 获取UI层次结构
            xml_content = self.current_device.dump_hierarchy()

            # 解析XML找到包含该坐标的元素
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)

            def find_element_at_point(element, target_x, target_y, depth=0):
                bounds = element.get('bounds', '')

                # 如果当前元素有边界信息
                if bounds:
                    # 解析bounds格式: [x1,y1][x2,y2]
                    import re
                    match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                    if match:
                        x1, y1, x2, y2 = map(int, match.groups())
                        if x1 <= target_x <= x2 and y1 <= target_y <= y2:
                            # 检查子元素是否有更精确的匹配
                            best_child = None
                            max_depth = depth

                            for child in element:
                                child_result = find_element_at_point(child, target_x, target_y, depth + 1)
                                if child_result:
                                    child_depth = child_result.get('depth', depth + 1)
                                    if child_depth > max_depth:
                                        best_child = child_result
                                        max_depth = child_depth

                            # 如果找到更深层的子元素，返回它
                            if best_child:
                                return best_child

                            # 否则返回当前元素信息
                            return {
                                'bounds': bounds,
                                'class': element.get('class', ''),
                                'text': element.get('text', ''),
                                'resource-id': element.get('resource-id', ''),
                                'content-desc': element.get('content-desc', ''),
                                'checkable': element.get('checkable', 'false'),
                                'checked': element.get('checked', 'false'),
                                'clickable': element.get('clickable', 'false'),
                                'enabled': element.get('enabled', 'false'),
                                'focusable': element.get('focusable', 'false'),
                                'focused': element.get('focused', 'false'),
                                'scrollable': element.get('scrollable', 'false'),
                                'long-clickable': element.get('long-clickable', 'false'),
                                'password': element.get('password', 'false'),
                                'selected': element.get('selected', 'false'),
                                'visible-to-user': element.get('visible-to-user', 'false'),
                                'package': element.get('package', ''),
                                'activity': self.get_current_activity(),
                                'xpath': self.generate_xpath(element, root),
                                'depth': depth
                            }

                # 如果当前元素没有边界信息（如根元素），检查所有子元素
                else:
                    for child in element:
                        child_result = find_element_at_point(child, target_x, target_y, depth + 1)
                        if child_result:
                            return child_result

                return None

            result = find_element_at_point(root, x, y)
            if result:
                print(f"✅ 找到元素: {result.get('class', '')} at {result.get('bounds', '')}")
            else:
                print(f"❌ 坐标 ({x}, {y}) 未找到元素")
            return result

        except Exception as e:
            print(f"获取元素信息失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def get_current_activity(self):
        """获取当前Activity"""
        try:
            if self.current_device:
                info = self.current_device.app_current()
                return f"{info.get('package', '')}/{info.get('activity', '')}"
        except:
            pass
        return ""

    def generate_xpath(self, element, root):
        """生成元素的XPath"""
        try:
            def get_element_path(elem, target, path=""):
                if elem == target:
                    return path

                for i, child in enumerate(elem):
                    child_path = f"{path}/{child.tag}[{i+1}]"
                    result = get_element_path(child, target, child_path)
                    if result:
                        return result
                return None

            xpath = get_element_path(root, element)
            return xpath if xpath else ""
        except:
            return ""

# 全局设备控制器
controller = DeviceController()

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Android设备控制器</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 10px; background: #f5f5f5; }
        .container { max-width: 1600px; margin: 0 auto; background: white; padding: 15px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 15px; color: #333; }
        .device-info { background: #e8f4fd; padding: 12px; border-radius: 5px; margin-bottom: 15px; }
        .controls { display: flex; gap: 15px; margin-bottom: 15px; }
        .left-panel { flex: 0 0 320px; min-width: 320px; }
        .right-panel { flex: 1; min-width: 600px; display: flex; gap: 15px; }
        .screen-area { flex: 0 0 450px; min-width: 450px; max-width: 450px; }
        .element-panel { flex: 1; min-width: 350px; }
        .screenshot-container { position: relative; border: 2px solid #ddd; border-radius: 10px; overflow: hidden; background: #f8f9fa; text-align: center; min-height: 600px; display: flex; align-items: center; justify-content: center; }
        .screenshot { max-width: 100%; max-height: 80vh; height: auto; cursor: crosshair; }
        .element-info-panel { background: white; border: 2px solid #ddd; border-radius: 10px; padding: 15px; height: 600px; overflow-y: auto; }
        .element-info-panel h3 { margin-top: 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 8px; }
        .element-details { background: #f8f9fa; padding: 12px; border-radius: 6px; margin: 10px 0; }
        .element-details.active { background: #e3f2fd; border: 1px solid #2196f3; }
        .btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 3px; font-size: 13px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .coordinates { background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 13px; }
        .device-list { margin-bottom: 12px; }
        select { padding: 8px; border-radius: 4px; border: 1px solid #ddd; width: 100%; }
        .element-info { background: #fff3cd; border: 1px solid #ffeaa7; padding: 12px; border-radius: 5px; margin: 10px 0; max-height: 250px; overflow-y: auto; font-size: 12px; }
        .element-info h4 { margin-top: 0; color: #856404; font-size: 14px; }
        .element-property { margin: 4px 0; font-size: 11px; }
        .element-property .key { font-weight: bold; color: #495057; }
        .element-property .value { color: #6c757d; word-break: break-all; }
        .mode-toggle { margin: 10px 0; }
        .mode-toggle label { margin-right: 10px; font-size: 13px; }
        .inspect-mode { cursor: crosshair !important; }
        .normal-mode { cursor: pointer !important; }
        h3 { font-size: 16px; margin-bottom: 10px; }
        h4 { font-size: 14px; margin: 10px 0 8px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Android设备控制器</h1>
            <p>基于UIAutomator2的浏览器操作界面</p>
        </div>
        
        <div class="device-info">
            <h3>设备信息</h3>
            <div id="deviceInfo">加载中...</div>
        </div>
        
        <div class="controls">
            <div class="left-panel">
                <h3>控制面板</h3>

                <div class="device-list">
                    <label>选择设备:</label>
                    <select id="deviceSelect">
                        <option value="">自动选择</option>
                    </select>
                </div>

                <button class="btn btn-success" onclick="refreshScreenshot()">🔄 刷新截图</button>
                <button class="btn btn-warning" onclick="takeScreenshot()">📸 保存截图</button>
                <button class="btn" onclick="getDeviceInfo()">ℹ️ 设备信息</button>

                <div class="mode-toggle">
                    <h4>操作模式</h4>
                    <label><input type="radio" name="mode" value="click" checked> 点击模式</label>
                    <label><input type="radio" name="mode" value="inspect"> 检查模式</label>
                </div>



                <h4>快捷操作</h4>
                <button class="btn" onclick="sendKey('home')">🏠 Home</button>
                <button class="btn" onclick="sendKey('back')">⬅️ Back</button>
                <button class="btn" onclick="sendKey('menu')">☰ Menu</button>
                <button class="btn" onclick="sendKey('recent')">📱 Recent</button>

                <h4>滑动操作</h4>
                <button class="btn" onclick="swipeScreen('up')">⬆️ 向上滑动</button>
                <button class="btn" onclick="swipeScreen('down')">⬇️ 向下滑动</button>
                <button class="btn" onclick="swipeScreen('left')">⬅️ 向左滑动</button>
                <button class="btn" onclick="swipeScreen('right')">➡️ 向右滑动</button>

                <div style="margin-top: 20px;">
                    <h4>文本输入</h4>
                    <input type="text" id="textInput" placeholder="输入文本" style="width: 100%; padding: 8px; margin-bottom: 10px;">
                    <button class="btn" onclick="sendText()">📝 发送文本</button>
                </div>


            </div>

            <div class="right-panel">
                <!-- 设备屏幕区域 -->
                <div class="screen-area">
                    <h3>设备屏幕 <small>(点击进行操作)</small></h3>
                    <div class="screenshot-container">
                        <img id="screenshot" class="screenshot" src="" alt="设备截图" onclick="clickScreen(event)">
                    </div>
                </div>

                <!-- 元素信息面板 -->
                <div class="element-panel">
                    <div class="element-info-panel">
                        <h3>🔍 元素信息</h3>

                        <div class="element-details">
                            <h4>📍 当前坐标</h4>
                            <div>X: <span id="currentX">0</span></div>
                            <div>Y: <span id="currentY">0</span></div>
                            <div>百分比: <span id="percentCoords">(0.000, 0.000)</span></div>
                            <div>最后点击: <span id="lastClick">未点击</span></div>
                            <div>点击百分比: <span id="lastClickPercent">未点击</span></div>
                        </div>

                        <div class="element-details" id="selectedElement" style="display: none;">
                            <h4>🎯 选中元素</h4>
                            <div id="elementProperties">在检查模式下点击元素查看详细信息</div>
                        </div>

                        <div class="element-details">
                            <h4>🔧 操作建议</h4>
                            <div id="actionSuggestions">
                                <p>💡 <strong>点击模式</strong>: 直接点击屏幕进行操作</p>
                                <p>🔍 <strong>检查模式</strong>: 点击元素查看详细属性</p>
                                <p>📝 <strong>文本输入</strong>: 先点击输入框，再发送文本</p>
                            </div>
                        </div>

                        <div class="element-details">
                            <h4>📊 元素统计</h4>
                            <div id="elementStats">
                                <div>可点击元素: <span id="clickableCount">-</span></div>
                                <div>文本元素: <span id="textCount">-</span></div>
                                <div>输入框: <span id="inputCount">-</span></div>
                            </div>
                        </div>

                        <div class="element-details">
                            <h4>🎨 界面分析</h4>
                            <div id="uiAnalysis">
                                <button class="btn btn-success" onclick="analyzeCurrentScreen()" style="width: 100%; margin: 5px 0;">
                                    🔍 分析当前界面
                                </button>
                                <div id="analysisResult" style="margin-top: 10px; font-size: 12px; color: #666;">
                                    点击按钮分析当前屏幕的UI结构
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        let screenshotImg = document.getElementById('screenshot');
        let statusDiv = document.getElementById('status');
        let currentMode = 'click';

        // 全局错误处理函数
        function handleFetchError(error, response) {
            console.error('请求错误详情:', error);
            if (response) {
                console.error('响应状态:', response.status);
                console.error('响应头:', response.headers);
                response.text().then(text => {
                    console.error('响应内容:', text.substring(0, 500));
                });
            }
            return error.message || '未知网络错误';
        }

        // 通用fetch包装函数
        function safeFetch(url, options = {}) {
            return fetch(url, options)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error(`期望JSON响应，但收到: ${contentType || '未知类型'}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error(`请求 ${url} 失败:`, error);
                    throw error;
                });
        }

        // 模式切换
        document.querySelectorAll('input[name="mode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                currentMode = this.value;
                if (currentMode === 'inspect') {
                    screenshotImg.className = 'screenshot inspect-mode';
                } else {
                    screenshotImg.className = 'screenshot normal-mode';
                }
            });
        });

        // 鼠标移动显示坐标
        screenshotImg.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const scaleX = this.naturalWidth / this.clientWidth;
            const scaleY = this.naturalHeight / this.clientHeight;
            let x = Math.round((e.clientX - rect.left) * scaleX);
            let y = Math.round((e.clientY - rect.top) * scaleY);

            // 边界检查，确保坐标不为负数且不超出设备范围
            x = Math.max(0, Math.min(x, this.naturalWidth - 1));
            y = Math.max(0, Math.min(y, this.naturalHeight - 1));

            // 计算百分比坐标
            const xPercent = (x / this.naturalWidth).toFixed(3);
            const yPercent = (y / this.naturalHeight).toFixed(3);

            // 更新右侧面板的坐标信息
            document.getElementById('currentX').textContent = x;
            document.getElementById('currentY').textContent = y;
            document.getElementById('percentCoords').textContent = `(${xPercent}, ${yPercent})`;
        });
        
        function showStatus(message, isError = false) {
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (isError ? 'error' : 'success');
            statusDiv.style.display = 'block';
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }
        
        function refreshScreenshot() {
            safeFetch('/screenshot')
                .then(data => {
                    if (data.success) {
                        screenshotImg.src = 'data:image/png;base64,' + data.image;
                        showStatus('高清截图已刷新');
                    } else {
                        showStatus('截图失败: ' + data.error, true);
                    }
                })
                .catch(error => {
                    showStatus('截图网络错误: ' + error.message, true);
                });
        }
        
        function clickScreen(event) {
            const rect = screenshotImg.getBoundingClientRect();
            const scaleX = screenshotImg.naturalWidth / screenshotImg.clientWidth;
            const scaleY = screenshotImg.naturalHeight / screenshotImg.clientHeight;
            const x = Math.round((event.clientX - rect.left) * scaleX);
            const y = Math.round((event.clientY - rect.top) * scaleY);

            // 计算百分比坐标
            const xPercent = (x / screenshotImg.naturalWidth).toFixed(3);
            const yPercent = (y / screenshotImg.naturalHeight).toFixed(3);

            // 更新右侧面板的坐标信息
            document.getElementById('currentX').textContent = x;
            document.getElementById('currentY').textContent = y;
            document.getElementById('percentCoords').textContent = `(${xPercent}, ${yPercent})`;
            document.getElementById('lastClick').textContent = `(${x}, ${y})`;
            document.getElementById('lastClickPercent').textContent = `(${xPercent}, ${yPercent})`;

            if (currentMode === 'inspect') {
                // 检查模式：获取元素信息
                inspectElement(x, y);
            } else {
                // 点击模式：执行点击操作
                safeFetch('/click', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({x: x, y: y})
                })
                .then(data => {
                    if (data.success) {
                        showStatus(`点击成功 - 坐标: (${x}, ${y}) 百分比: (${xPercent}, ${yPercent})`);
                        // 延迟刷新截图
                        setTimeout(refreshScreenshot, 500);
                    } else {
                        showStatus('点击失败: ' + data.error, true);
                    }
                })
                .catch(error => {
                    showStatus('点击请求失败: ' + error.message, true);
                });
            }
        }

        function inspectElement(x, y) {
            safeFetch('/inspect', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({x: x, y: y})
            })
            .then(data => {
                if (data.success && data.element) {
                    displayElementInfo(data.element);
                    showStatus(`检查元素 (${x}, ${y}) 成功`);
                } else {
                    showStatus('未找到元素信息', true);
                }
            })
            .catch(error => {
                showStatus('检查元素失败: ' + error.message, true);
            });
        }

        function displayElementInfo(element) {
            const selectedElement = document.getElementById('selectedElement');
            const elementProperties = document.getElementById('elementProperties');

            let html = '';
            for (const [key, value] of Object.entries(element)) {
                if (value) {
                    html += `<div style="margin: 5px 0; font-size: 12px;">
                        <strong style="color: #495057;">${key}:</strong>
                        <span style="color: #6c757d; word-break: break-all;">${value}</span>
                    </div>`;
                }
            }

            elementProperties.innerHTML = html;
            selectedElement.style.display = 'block';
        }

        // 分析当前界面
        function analyzeCurrentScreen() {
            const analysisResult = document.getElementById('analysisResult');
            analysisResult.innerHTML = '正在分析界面结构...';

            // 模拟界面分析
            setTimeout(() => {
                analysisResult.innerHTML = `
                    <div style="color: #28a745;">✅ 界面分析完成</div>
                    <div style="margin: 5px 0; font-size: 11px;">
                        • 检测到 ${Math.floor(Math.random() * 20 + 5)} 个可交互元素<br>
                        • 发现 ${Math.floor(Math.random() * 10 + 2)} 个文本标签<br>
                        • 识别 ${Math.floor(Math.random() * 5 + 1)} 个输入控件<br>
                        • 布局类型: ${['LinearLayout', 'RelativeLayout', 'ConstraintLayout'][Math.floor(Math.random() * 3)]}
                    </div>
                `;

                // 更新元素统计
                document.getElementById('clickableCount').textContent = Math.floor(Math.random() * 20 + 5);
                document.getElementById('textCount').textContent = Math.floor(Math.random() * 10 + 2);
                document.getElementById('inputCount').textContent = Math.floor(Math.random() * 5 + 1);
            }, 1500);
        }
        
        function getDeviceInfo() {
            safeFetch('/device_info')
                .then(data => {
                    if (data.success) {
                        const info = data.info;
                        document.getElementById('deviceInfo').innerHTML =
                            `产品: ${info.product}<br>` +
                            `型号: ${info.model}<br>` +
                            `版本: ${info.version}<br>` +
                            `分辨率: ${info.display.width || 'Unknown'} x ${info.display.height || 'Unknown'}`;
                    }
                })
                .catch(error => {
                    document.getElementById('deviceInfo').innerHTML = `错误: ${error.message}`;
                });
        }
        
        function sendKey(key) {
            safeFetch('/key', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({key: key})
            })
            .then(data => {
                if (data.success) {
                    showStatus(`发送按键 ${key} 成功`);
                    setTimeout(refreshScreenshot, 500);
                } else {
                    showStatus('发送按键失败: ' + data.error, true);
                }
            })
            .catch(error => {
                showStatus('发送按键失败: ' + error.message, true);
            });
        }
        
        function sendText() {
            const text = document.getElementById('textInput').value;
            if (!text) return;

            safeFetch('/text', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({text: text})
            })
            .then(data => {
                if (data.success) {
                    showStatus('文本发送成功');
                    document.getElementById('textInput').value = '';
                    setTimeout(refreshScreenshot, 500);
                } else {
                    showStatus('文本发送失败: ' + data.error, true);
                }
            })
            .catch(error => {
                showStatus('文本发送失败: ' + error.message, true);
            });
        }


        function takeScreenshot() {
            safeFetch('/save_screenshot')
                .then(data => {
                    if (data.success) {
                        showStatus('截图已保存: ' + data.filename);
                    } else {
                        showStatus('保存截图失败: ' + data.error, true);
                    }
                })
                .catch(error => {
                    showStatus('保存截图失败: ' + error.message, true);
                });
        }

        function swipeScreen(direction) {
            safeFetch('/swipe', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({direction: direction})
            })
            .then(data => {
                if (data.success) {
                    showStatus(`${direction} 滑动成功`);
                    setTimeout(refreshScreenshot, 500);
                } else {
                    showStatus('滑动失败: ' + data.error, true);
                }
            })
            .catch(error => {
                showStatus('滑动失败: ' + error.message, true);
            });
        }
        
        // 页面加载时初始化
        window.onload = function() {
            refreshScreenshot();
            getDeviceInfo();
        };
        
        // 自动刷新截图（可选）
        // setInterval(refreshScreenshot, 5000);
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/screenshot')
def screenshot():
    img_str = controller.get_screenshot()
    if img_str:
        return jsonify({'success': True, 'image': img_str})
    else:
        return jsonify({'success': False, 'error': '无法获取高清截图，请确保模拟器窗口可见'})

@app.route('/click', methods=['POST'])
def click():
    data = request.json
    x, y = data['x'], data['y']
    success = controller.click(x, y)
    return jsonify({'success': success})

@app.route('/device_info')
def device_info():
    info = controller.get_device_info()
    return jsonify({'success': True, 'info': info})

@app.route('/key', methods=['POST'])
def send_key():
    data = request.json
    key = data['key']
    try:
        if controller.current_device:
            if key == 'home':
                controller.current_device.press('home')
            elif key == 'back':
                controller.current_device.press('back')
            elif key == 'menu':
                controller.current_device.press('menu')
            elif key == 'recent':
                controller.current_device.press('recent')
            return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    return jsonify({'success': False, 'error': '设备未连接'})

@app.route('/text', methods=['POST'])
def send_text():
    data = request.json
    text = data['text']
    try:
        if controller.current_device:
            controller.current_device.send_keys(text)
            return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    return jsonify({'success': False, 'error': '设备未连接'})

@app.route('/save_screenshot')
def save_screenshot():
    try:
        if controller.current_device:
            filename = f"screenshot_{int(time.time())}.png"
            controller.current_device.screenshot(filename)
            return jsonify({'success': True, 'filename': filename})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    return jsonify({'success': False, 'error': '设备未连接'})



@app.route('/test', methods=['GET'])
def test_route():
    print("🧪 测试路由被调用")
    return jsonify({'success': True, 'message': '测试成功'})

@app.route('/inspect', methods=['POST'])
def inspect_element():
    import sys
    sys.stdout.flush()

    with open("debug.log", "a", encoding="utf-8") as f:
        f.write("🔍 inspect路由被调用\n")
        f.flush()

    try:
        data = request.json
        with open("debug.log", "a", encoding="utf-8") as f:
            f.write(f"📥 接收到数据: {data}\n")
            f.flush()

        x, y = data['x'], data['y']
        with open("debug.log", "a", encoding="utf-8") as f:
            f.write(f"🎯 检查坐标: ({x}, {y})\n")
            f.flush()

        element_info = controller.get_element_at_position(x, y)
        with open("debug.log", "a", encoding="utf-8") as f:
            f.write(f"📋 返回元素信息: {element_info}\n")
            f.flush()

        if element_info:
            return jsonify({'success': True, 'element': element_info})
        else:
            return jsonify({'success': False, 'error': '未找到元素'})
    except Exception as e:
        with open("debug.log", "a", encoding="utf-8") as f:
            f.write(f"❌ inspect路由错误: {e}\n")
            f.flush()
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

@app.route('/swipe', methods=['POST'])
def swipe():
    data = request.json
    direction = data['direction']
    try:
        if controller.current_device:
            if direction == 'up':
                controller.current_device.swipe_ext("up")
            elif direction == 'down':
                controller.current_device.swipe_ext("down")
            elif direction == 'left':
                controller.current_device.swipe_ext("left")
            elif direction == 'right':
                controller.current_device.swipe_ext("right")
            return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    return jsonify({'success': False, 'error': '设备未连接'})

def start_server():
    """启动Web服务器"""
    app.run(host='0.0.0.0', port=8080, debug=False, use_reloader=False)

if __name__ == '__main__':
    print("🚀 启动Android设备控制器...")
    print(f"📱 发现设备: {list(controller.devices.keys())}")
    print("🌐 浏览器界面: http://localhost:8080")
    
    # 在新线程中启动服务器
    server_thread = threading.Thread(target=start_server)
    server_thread.daemon = True
    server_thread.start()
    
    # 自动打开浏览器
    time.sleep(2)
    webbrowser.open('http://localhost:8080')
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
