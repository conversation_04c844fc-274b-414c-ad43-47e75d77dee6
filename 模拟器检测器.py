#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器检测器 - 检测可用的雷电模拟器
"""

import os
import sys
import subprocess

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from 雷电API import Dnconsole

def find_leidian_path():
    """查找雷电模拟器安装路径"""
    print("🔍 查找雷电模拟器安装路径...")
    
    base_paths = [
        r"G:\leidian\LDPlayer9",
        r"G:\LDPlayer\LDPlayer9", 
        r"C:\LDPlayer\LDPlayer9",
        r"D:\LDPlayer\LDPlayer9",
        r"E:\LDPlayer\LDPlayer9",
        r"F:\LDPlayer\LDPlayer9"
    ]
    
    found_paths = []
    for path in base_paths:
        ld_exe = os.path.join(path, "ld.exe")
        ldconsole_exe = os.path.join(path, "ldconsole.exe")
        
        if os.path.exists(ld_exe) and os.path.exists(ldconsole_exe):
            found_paths.append(path)
            print(f"✅ 找到雷电模拟器: {path}")
    
    if not found_paths:
        print("❌ 未找到雷电模拟器安装路径")
        return None
    
    return found_paths[0]  # 返回第一个找到的路径

def list_emulators(base_path):
    """列出所有模拟器"""
    print("\n📱 检测模拟器列表...")
    
    try:
        share_path = os.path.expanduser("~/Documents/leidian64")
        ld = Dnconsole(base_path=base_path, share_path=share_path)
        
        # 获取模拟器列表
        emulator_list = ld.get_list()
        
        if not emulator_list:
            print("❌ 未找到任何模拟器")
            return []
        
        print(f"找到 {len(emulator_list)} 个模拟器:")
        print("-" * 80)
        print("索引 | 名称 | 顶层句柄 | 绑定句柄 | Android状态 | 进程PID | VBox PID")
        print("-" * 80)
        
        for emulator in emulator_list:
            if len(emulator) >= 7:
                index, name, top_hwnd, bind_hwnd, android_status, pid, vbox_pid = emulator[:7]
                android_text = "✅ 已启动" if android_status == "1" else "❌ 未启动"
                print(f"{index:4} | {name:10} | {top_hwnd:8} | {bind_hwnd:8} | {android_text:8} | {pid:7} | {vbox_pid}")
        
        return emulator_list
        
    except Exception as e:
        print(f"❌ 获取模拟器列表失败: {e}")
        return []

def test_emulator_connection(base_path, index):
    """测试特定模拟器的连接"""
    print(f"\n🔧 测试模拟器 {index} 的连接...")
    
    try:
        share_path = os.path.expanduser("~/Documents/leidian64")
        ld = Dnconsole(base_path=base_path, share_path=share_path, emulator_id=index)
        
        # 测试基本连接
        success, output = ld.execute_ld(index, "echo 'Hello World'")
        if success and output:
            print(f"✅ 基本连接成功: {output}")
        else:
            print(f"❌ 基本连接失败")
            return False
        
        # 测试Android系统
        success, output = ld.execute_ld(index, "getprop ro.build.version.release")
        if success and output:
            print(f"✅ Android系统版本: {output}")
        else:
            print(f"❌ Android系统未就绪")
            return False
        
        # 测试root权限
        success, output = ld.execute_ld(index, "su -c 'whoami'")
        if success and "root" in output:
            print(f"✅ Root权限正常")
        else:
            print(f"❌ Root权限获取失败")
            return False
        
        # 检查Facebook应用
        success, output = ld.execute_ld(index, "pm list packages | grep facebook")
        if success and "com.facebook.katana" in output:
            print(f"✅ Facebook应用已安装")
        else:
            print(f"❌ Facebook应用未安装")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 雷电模拟器检测器")
    print("=" * 50)
    
    # 1. 查找雷电模拟器路径
    base_path = find_leidian_path()
    if not base_path:
        print("\n❌ 请先安装雷电模拟器")
        return
    
    # 2. 列出所有模拟器
    emulator_list = list_emulators(base_path)
    if not emulator_list:
        print("\n❌ 请先创建模拟器")
        return
    
    # 3. 测试每个运行中的模拟器
    print("\n🔧 测试运行中的模拟器...")
    available_emulators = []
    
    for emulator in emulator_list:
        if len(emulator) >= 5:
            index, name, _, _, android_status = emulator[:5]
            
            if android_status == "1":  # Android已启动
                print(f"\n测试模拟器 {index} ({name}):")
                if test_emulator_connection(base_path, int(index)):
                    available_emulators.append(int(index))
                    print(f"✅ 模拟器 {index} 可用于2FA提取")
                else:
                    print(f"❌ 模拟器 {index} 不可用")
    
    # 4. 显示推荐
    print("\n📋 检测结果:")
    print("=" * 30)
    
    if available_emulators:
        print(f"✅ 找到 {len(available_emulators)} 个可用模拟器:")
        for index in available_emulators:
            print(f"   - 模拟器 {index}")
        
        print(f"\n💡 建议使用模拟器 {available_emulators[0]} 进行2FA提取")
        print(f"修改提取器代码中的 emulator_index={available_emulators[0]}")
        
        # 生成修改建议
        print(f"\n📝 修改建议:")
        print(f"在 修复版FB_2FA提取器.py 中:")
        print(f"将 emulator_index=11 改为 emulator_index={available_emulators[0]}")
        
    else:
        print("❌ 未找到可用的模拟器")
        print("\n💡 解决建议:")
        print("1. 启动一个模拟器")
        print("2. 确保模拟器已开启root权限")
        print("3. 在模拟器中安装Facebook应用")
        print("4. 登录Facebook账户并启用2FA")

if __name__ == "__main__":
    main()
