#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web API的元素检查功能
"""

import requests
import json

def test_inspect_api():
    """测试检查API"""
    print("🔍 测试Web API元素检查功能...")
    
    # 测试坐标
    test_coordinates = [
        (130, 200),
        (50, 50),
        (17, 32),
        (219, 32),
        (100, 300)
    ]
    
    for x, y in test_coordinates:
        print(f"\n📍 测试坐标 ({x}, {y}):")
        
        try:
            # 发送检查请求
            response = requests.post(
                'http://localhost:8080/inspect',
                json={'x': x, 'y': y},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('element'):
                    element = data['element']
                    print(f"✅ 找到元素:")
                    print(f"   类名: {element.get('class', '')}")
                    print(f"   边界: {element.get('bounds', '')}")
                    print(f"   文本: '{element.get('text', '')}'")
                    print(f"   资源ID: {element.get('resource-id', '')}")
                    print(f"   可点击: {element.get('clickable', 'false')}")
                    print(f"   Activity: {element.get('activity', '')}")
                    print(f"   XPath: {element.get('xpath', '')}")
                else:
                    print(f"❌ 未找到元素: {data.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_swipe_api():
    """测试滑动API"""
    print("\n🔄 测试滑动API...")
    
    directions = ['up', 'down', 'left', 'right']
    
    for direction in directions:
        print(f"\n📱 测试 {direction} 滑动:")
        
        try:
            response = requests.post(
                'http://localhost:8080/swipe',
                json={'direction': direction},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ {direction} 滑动成功")
                else:
                    print(f"❌ {direction} 滑动失败: {data.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")

def test_device_info():
    """测试设备信息API"""
    print("\n📱 测试设备信息API...")
    
    try:
        response = requests.get('http://localhost:8080/device_info', timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 设备信息获取成功:")
            for key, value in data.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始Web API测试...")
    print("=" * 60)
    
    # 测试设备信息
    test_device_info()
    
    # 测试元素检查
    test_inspect_api()
    
    # 测试滑动功能
    test_swipe_api()
    
    print("\n" + "=" * 60)
    print("🎉 Web API测试完成！")
    
    print("\n📋 使用说明:")
    print("1. 如果元素检查功能正常，说明后端API工作正常")
    print("2. 如果浏览器界面显示'未找到元素'，可能是前端JavaScript问题")
    print("3. 请检查浏览器控制台是否有JavaScript错误")
    print("4. 确保已经强制刷新浏览器页面 (Ctrl+F5)")

if __name__ == "__main__":
    main()
