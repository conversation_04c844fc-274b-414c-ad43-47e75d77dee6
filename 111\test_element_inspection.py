#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI元素检查功能
"""

import uiautomator2 as u2
import xml.etree.ElementTree as ET
import re

def test_element_inspection():
    """测试元素检查功能"""
    print("🔍 测试UI元素检查功能...")
    
    try:
        # 连接设备
        d = u2.connect('127.0.0.1:5557')
        print(f"✅ 设备连接成功: {d.info.get('model', 'Unknown')}")
        
        # 获取UI层次结构
        print("📱 获取UI层次结构...")
        xml_content = d.dump_hierarchy()
        
        # 解析XML
        root = ET.fromstring(xml_content)
        print("✅ XML解析成功")
        
        # 查找所有可点击元素
        clickable_elements = []
        
        def find_clickable_elements(element):
            if element.get('clickable') == 'true':
                bounds = element.get('bounds', '')
                if bounds:
                    match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                    if match:
                        x1, y1, x2, y2 = map(int, match.groups())
                        center_x = (x1 + x2) // 2
                        center_y = (y1 + y2) // 2
                        
                        clickable_elements.append({
                            'bounds': bounds,
                            'center': (center_x, center_y),
                            'class': element.get('class', ''),
                            'text': element.get('text', ''),
                            'resource-id': element.get('resource-id', ''),
                            'content-desc': element.get('content-desc', ''),
                            'package': element.get('package', '')
                        })
            
            for child in element:
                find_clickable_elements(child)
        
        find_clickable_elements(root)
        
        print(f"🎯 发现 {len(clickable_elements)} 个可点击元素:")
        
        for i, elem in enumerate(clickable_elements[:10]):  # 只显示前10个
            print(f"\n元素 {i+1}:")
            print(f"  坐标: {elem['center']}")
            print(f"  类名: {elem['class']}")
            print(f"  文本: {elem['text']}")
            print(f"  资源ID: {elem['resource-id']}")
            print(f"  描述: {elem['content-desc']}")
            print(f"  包名: {elem['package']}")
            print(f"  边界: {elem['bounds']}")
        
        if len(clickable_elements) > 10:
            print(f"\n... 还有 {len(clickable_elements) - 10} 个元素")
        
        return clickable_elements
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def test_element_at_position(x, y):
    """测试指定坐标的元素检查"""
    print(f"\n🎯 测试坐标 ({x}, {y}) 处的元素...")
    
    try:
        d = u2.connect('127.0.0.1:5557')
        xml_content = d.dump_hierarchy()
        root = ET.fromstring(xml_content)
        
        def find_element_at_point(element, target_x, target_y):
            bounds = element.get('bounds', '')
            if bounds:
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    if x1 <= target_x <= x2 and y1 <= target_y <= y2:
                        # 检查子元素
                        for child in element:
                            child_result = find_element_at_point(child, target_x, target_y)
                            if child_result:
                                return child_result
                        
                        # 返回当前元素
                        return {
                            'bounds': bounds,
                            'class': element.get('class', ''),
                            'text': element.get('text', ''),
                            'resource-id': element.get('resource-id', ''),
                            'content-desc': element.get('content-desc', ''),
                            'checkable': element.get('checkable', 'false'),
                            'checked': element.get('checked', 'false'),
                            'clickable': element.get('clickable', 'false'),
                            'enabled': element.get('enabled', 'false'),
                            'focusable': element.get('focusable', 'false'),
                            'focused': element.get('focused', 'false'),
                            'scrollable': element.get('scrollable', 'false'),
                            'long-clickable': element.get('long-clickable', 'false'),
                            'password': element.get('password', 'false'),
                            'selected': element.get('selected', 'false'),
                            'visible-to-user': element.get('visible-to-user', 'false'),
                            'package': element.get('package', '')
                        }
            return None
        
        element = find_element_at_point(root, x, y)
        
        if element:
            print("✅ 找到元素:")
            for key, value in element.items():
                if value:
                    print(f"  {key}: {value}")
        else:
            print("❌ 未找到元素")
        
        return element
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def generate_code_examples(elements):
    """生成代码示例"""
    print("\n💻 生成UIAutomator2代码示例:")
    
    for i, elem in enumerate(elements[:5]):  # 前5个元素
        print(f"\n# 元素 {i+1} - {elem['class']}")
        
        # 通过坐标点击
        x, y = elem['center']
        print(f"d.click({x}, {y})  # 坐标点击")
        
        # 通过文本点击
        if elem['text']:
            print(f"d(text='{elem['text']}').click()  # 文本点击")
        
        # 通过资源ID点击
        if elem['resource-id']:
            print(f"d(resourceId='{elem['resource-id']}').click()  # 资源ID点击")
        
        # 通过类名点击
        if elem['class']:
            print(f"d(className='{elem['class']}').click()  # 类名点击")
        
        # 组合条件
        conditions = []
        if elem['text']:
            conditions.append(f"text='{elem['text']}'")
        if elem['class']:
            conditions.append(f"className='{elem['class']}'")
        if len(conditions) > 1:
            print(f"d({', '.join(conditions)}).click()  # 组合条件")

def main():
    """主函数"""
    print("🚀 开始UI元素检查测试...")
    print("=" * 60)
    
    # 测试元素发现
    elements = test_element_inspection()
    
    if elements:
        # 测试指定坐标元素检查
        if len(elements) > 0:
            test_x, test_y = elements[0]['center']
            test_element_at_position(test_x, test_y)
        
        # 生成代码示例
        generate_code_examples(elements)
        
        print("\n" + "=" * 60)
        print("🎉 测试完成！")
        print("\n📋 使用说明:")
        print("1. 在浏览器界面选择'检查模式'")
        print("2. 点击屏幕上的元素查看详细信息")
        print("3. 获取的信息包括:")
        print("   - XPath路径")
        print("   - 类名 (class)")
        print("   - 资源ID (resource-id)")
        print("   - 文本内容 (text)")
        print("   - 边界坐标 (bounds)")
        print("   - 各种属性 (clickable, enabled等)")
        
        print("\n💡 编程使用示例:")
        print("```python")
        print("import uiautomator2 as u2")
        print("d = u2.connect('127.0.0.1:5557')")
        print("# 通过文本点击")
        print("d(text='设置').click()")
        print("# 通过资源ID点击")
        print("d(resourceId='com.android.settings:id/title').click()")
        print("# 通过坐标点击")
        print("d.click(100, 200)")
        print("```")
    else:
        print("❌ 未发现可用元素，请检查设备连接")

if __name__ == "__main__":
    main()
