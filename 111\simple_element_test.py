#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的元素检查测试
"""

import uiautomator2 as u2
import xml.etree.ElementTree as ET
import re

def simple_element_check(x, y):
    """简单的元素检查"""
    print(f"🔍 检查坐标 ({x}, {y})")
    
    try:
        # 连接设备
        d = u2.connect('127.0.0.1:5557')
        print("✅ 设备连接成功")
        
        # 获取UI层次结构
        xml_content = d.dump_hierarchy()
        print(f"✅ 获取UI层次结构成功，长度: {len(xml_content)}")
        
        # 解析XML
        root = ET.fromstring(xml_content)
        print("✅ XML解析成功")
        
        # 查找元素
        def find_element_at_point(element, target_x, target_y, depth=0):
            bounds = element.get('bounds', '')
            if bounds:
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    if x1 <= target_x <= x2 and y1 <= target_y <= y2:
                        # 检查子元素
                        best_child = None
                        max_depth = depth
                        
                        for child in element:
                            child_result = find_element_at_point(child, target_x, target_y, depth + 1)
                            if child_result and child_result[1] > max_depth:
                                best_child = child_result[0]
                                max_depth = child_result[1]
                        
                        if best_child:
                            return (best_child, max_depth)
                        
                        # 返回当前元素
                        return ({
                            'bounds': bounds,
                            'class': element.get('class', ''),
                            'text': element.get('text', ''),
                            'resource-id': element.get('resource-id', ''),
                            'content-desc': element.get('content-desc', ''),
                            'clickable': element.get('clickable', 'false'),
                            'enabled': element.get('enabled', 'false'),
                            'package': element.get('package', ''),
                            'depth': depth
                        }, depth)
            return None
        
        result = find_element_at_point(root, x, y)
        
        if result:
            element_info, depth = result
            print(f"✅ 找到元素 (深度 {depth}):")
            for key, value in element_info.items():
                if value and key != 'depth':
                    print(f"   {key}: {value}")
            return element_info
        else:
            print("❌ 未找到元素")
            return None
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_known_coordinates():
    """测试已知坐标"""
    # 这些坐标我们知道应该有元素
    test_coords = [
        (17, 32),   # ImageButton
        (219, 32),  # TextView
        (130, 200), # LinearLayout
        (100, 300), # 另一个元素
    ]
    
    print("🧪 测试已知坐标...")
    
    for x, y in test_coords:
        print(f"\n{'='*50}")
        result = simple_element_check(x, y)
        if result:
            print(f"✅ 坐标 ({x}, {y}) 检查成功")
        else:
            print(f"❌ 坐标 ({x}, {y}) 检查失败")

if __name__ == "__main__":
    test_known_coordinates()
