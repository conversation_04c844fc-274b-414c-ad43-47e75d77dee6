#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动TOTP搜索器 - 专门搜索真正的TOTP密钥
"""

import os
import sys
import re
import json
from datetime import datetime

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from 雷电API import Dnconsole

class ManualTOTPSearcher:
    def __init__(self, emulator_index=1):
        """初始化手动TOTP搜索器"""
        # 雷电模拟器路径配置
        base_paths = [
            r"G:\leidian\LDPlayer9",
            r"G:\LDPlayer\LDPlayer9", 
            r"C:\LDPlayer\LDPlayer9",
            r"D:\LDPlayer\LDPlayer9",
            r"E:\LDPlayer\LDPlayer9"
        ]
        
        self.base_path = None
        for path in base_paths:
            if os.path.exists(os.path.join(path, "ld.exe")):
                self.base_path = path
                break
        
        if not self.base_path:
            raise FileNotFoundError("未找到雷电模拟器安装路径")
        
        self.share_path = os.path.expanduser("~/Documents/leidian64")
        self.emulator_index = emulator_index
        
        # 初始化雷电控制台
        self.ld = Dnconsole(
            base_path=self.base_path,
            share_path=self.share_path,
            emulator_id=emulator_index
        )
        
        print(f"🔍 手动TOTP搜索器初始化成功")
        print(f"📱 目标模拟器: {emulator_index}")

    def execute_root_command(self, command):
        """执行root命令"""
        try:
            # 直接使用su执行命令
            root_command = f'su -c "{command}"'
            success, output = self.ld.execute_ld(self.emulator_index, root_command)
            return output if success else None
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return None

    def search_specific_patterns(self):
        """搜索特定的TOTP模式"""
        print("\n🎯 搜索特定TOTP模式...")
        print("=" * 50)
        
        # 1. 搜索标准26字符Base32密钥
        print("1️⃣ 搜索26字符Base32密钥...")
        result = self.execute_root_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null")
        if result:
            lines = [line.strip() for line in result.split('\n') if line.strip()]
            valid_keys = []
            for line in lines:
                if self.is_valid_totp_key(line):
                    valid_keys.append(line)
                    print(f"   🔑 找到有效密钥: {line}")
            
            if not valid_keys:
                print("   ❌ 未找到有效的26字符TOTP密钥")
        else:
            print("   ❌ 搜索失败")
        
        # 2. 搜索32字符Base32密钥
        print("\n2️⃣ 搜索32字符Base32密钥...")
        result = self.execute_root_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null")
        if result:
            lines = [line.strip() for line in result.split('\n') if line.strip()]
            valid_keys = []
            for line in lines:
                if self.is_valid_totp_key(line):
                    valid_keys.append(line)
                    print(f"   🔑 找到有效密钥: {line}")
            
            if not valid_keys:
                print("   ❌ 未找到有效的32字符TOTP密钥")
        else:
            print("   ❌ 搜索失败")
        
        # 3. 搜索十六进制编码的密钥
        print("\n3️⃣ 搜索十六进制编码密钥...")
        result = self.execute_root_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null")
        if result:
            lines = [line.strip() for line in result.split('\n') if line.strip()]
            for line in lines:
                try:
                    # 尝试将十六进制转换为Base32
                    import binascii
                    import base64
                    decoded_bytes = binascii.unhexlify(line)
                    base32_key = base64.b32encode(decoded_bytes).decode().rstrip('=')
                    if len(base32_key) == 26 and self.is_valid_totp_key(base32_key):
                        print(f"   🔓 十六进制转换成功: {line} -> {base32_key}")
                except:
                    continue
        
        # 4. 搜索数据库中的密钥
        print("\n4️⃣ 搜索数据库文件...")
        self.search_in_databases()
        
        # 5. 搜索特定关键词
        print("\n5️⃣ 搜索特定关键词...")
        keywords = ["secret", "totp", "authenticator", "backup", "recovery"]
        for keyword in keywords:
            print(f"   搜索关键词: {keyword}")
            result = self.execute_root_command(f"find /data/data/com.facebook.katana/ -type f -exec grep -l -i '{keyword}' {{}} \\; 2>/dev/null")
            if result:
                files = [f.strip() for f in result.split('\n') if f.strip()]
                print(f"     找到 {len(files)} 个包含'{keyword}'的文件")
                for file_path in files[:3]:  # 只显示前3个
                    print(f"       📄 {file_path}")

    def search_in_databases(self):
        """在数据库中搜索"""
        # 查找数据库文件
        result = self.execute_root_command("find /data/data/com.facebook.katana/ -name '*.db' -o -name '*.sqlite' -o -name '*.sqlite3' 2>/dev/null")
        
        if result:
            db_files = [f.strip() for f in result.split('\n') if f.strip()]
            print(f"   发现 {len(db_files)} 个数据库文件")
            
            for db_file in db_files:
                print(f"     📊 分析: {os.path.basename(db_file)}")
                
                # 使用strings提取可读字符串
                strings_result = self.execute_root_command(f"strings '{db_file}' | grep -E '[A-Z2-7]{{26}}|[A-Z2-7]{{32}}' 2>/dev/null")
                if strings_result:
                    lines = [line.strip() for line in strings_result.split('\n') if line.strip()]
                    for line in lines:
                        if self.is_valid_totp_key(line):
                            print(f"       🔑 数据库中找到密钥: {line}")

    def is_valid_totp_key(self, key):
        """验证是否为有效的TOTP密钥"""
        try:
            # 基本长度检查
            if len(key) not in [20, 24, 26, 32]:
                return False
            
            # Base32字符检查
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # 排除明显的英文单词
            exclude_words = [
                'FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'ANDROID', 'SYSTEM',
                'AMBIENT', 'DIFFUSE', 'STATIC', 'ENVIRONMENT', 'TEXTURE', 'ROTATION',
                'FACTOR', 'SHADING', 'PARAMS', 'FAVORITE', 'MESSENGER', 'CONTACT',
                'ENCODING', 'REDIRECT', 'STANDALONE', 'DIALTONE', 'FBINTERNAL'
            ]
            
            key_upper = key.upper()
            for word in exclude_words:
                if word in key_upper:
                    return False
            
            # 检查字符重复度
            char_counts = {}
            for char in key:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            max_char_ratio = max(char_counts.values()) / len(key)
            if max_char_ratio > 0.4:  # 如果任何字符出现超过40%，可能不是真正的密钥
                return False
            
            # 尝试Base32解码
            try:
                import base64
                padded_key = key + '=' * (8 - len(key) % 8) % 8
                decoded = base64.b32decode(padded_key)
                return 10 <= len(decoded) <= 64
            except:
                return False
            
        except:
            return False

    def deep_file_analysis(self):
        """深度文件分析"""
        print("\n🔬 深度文件分析...")
        print("=" * 50)
        
        # 重点分析的文件
        important_files = [
            "/data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml",
            "/data/data/com.facebook.katana/shared_prefs/com.facebook.secure.switchoff.xml",
            "/data/data/com.facebook.katana/shared_prefs/authentication.xml",
            "/data/data/com.facebook.katana/files/auth_data",
        ]
        
        for file_path in important_files:
            print(f"\n📄 深度分析: {os.path.basename(file_path)}")
            
            # 读取文件内容
            content = self.execute_root_command(f"cat '{file_path}' 2>/dev/null")
            if content:
                print(f"   文件大小: {len(content)} 字符")
                
                # 搜索所有可能的编码格式
                self.analyze_content_for_totp(content, file_path)
            else:
                print(f"   ❌ 无法读取文件")

    def analyze_content_for_totp(self, content, source):
        """分析内容中的TOTP数据"""
        # 1. 搜索Base64编码的数据
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        base64_matches = re.findall(base64_pattern, content)
        
        for match in base64_matches:
            if len(match) >= 20:
                try:
                    import base64
                    decoded = base64.b64decode(match + '==')
                    # 尝试转换为Base32
                    base32_key = base64.b32encode(decoded).decode().rstrip('=')
                    if len(base32_key) in [26, 32] and self.is_valid_totp_key(base32_key):
                        print(f"   🔓 Base64解码成功: {match[:20]}... -> {base32_key}")
                except:
                    continue
        
        # 2. 搜索JSON中的可能密钥
        json_patterns = [
            r'"secret"[:\s]*"([^"]+)"',
            r'"key"[:\s]*"([^"]+)"',
            r'"totp"[:\s]*"([^"]+)"',
            r'"auth_key"[:\s]*"([^"]+)"',
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match) >= 20 and self.is_valid_totp_key(match):
                    print(f"   🔑 JSON中发现密钥: {match}")

    def run_manual_search(self):
        """运行手动搜索"""
        print("🔍 开始手动TOTP搜索")
        print("=" * 50)
        
        # 1. 搜索特定模式
        self.search_specific_patterns()
        
        # 2. 深度文件分析
        self.deep_file_analysis()
        
        print("\n📋 搜索完成")
        print("=" * 30)
        print("如果仍未找到TOTP密钥，可能的原因：")
        print("1. Facebook账户未启用双因素认证")
        print("2. 使用的是短信验证而非认证应用")
        print("3. 密钥存储在加密格式中")
        print("4. 需要在设置2FA时进行实时监控")

def main():
    """主函数"""
    print("🔍 手动TOTP搜索器")
    print("=" * 30)
    
    try:
        searcher = ManualTOTPSearcher(emulator_index=1)
        searcher.run_manual_search()
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
