#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单卡密验证系统
可以集成到任何Python程序中的授权验证系统
"""

import hashlib
import json
import os
import time
import requests
from datetime import datetime, timedelta
import uuid

class SimpleAuthSystem:
    def __init__(self, app_name="MyApp", server_url=None):
        """初始化授权系统"""
        self.app_name = app_name
        self.server_url = server_url or "http://localhost:8080/api"
        self.local_auth_file = f"{app_name}_auth.dat"
        self.machine_id = self.get_machine_id()
        
    def get_machine_id(self):
        """获取机器唯一标识"""
        try:
            # 使用多种硬件信息生成唯一ID
            import platform
            import subprocess
            
            # 获取CPU信息
            cpu_info = platform.processor()
            
            # 获取MAC地址
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            
            # 组合信息
            machine_info = f"{cpu_info}_{mac}_{platform.system()}_{platform.node()}"
            
            # 生成MD5哈希
            return hashlib.md5(machine_info.encode()).hexdigest()[:16]
            
        except:
            # 备用方案
            return hashlib.md5(str(uuid.getnode()).encode()).hexdigest()[:16]
    
    def generate_card_key(self, days=30, prefix="DEMO"):
        """生成卡密"""
        # 生成随机字符串
        import random
        import string

        # 时间戳
        timestamp = int(time.time())

        # 随机部分
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=8))

        # 组合卡密: PREFIX-DAYS-RANDOM-CHECKSUM
        card_data = f"{prefix}-{days:03d}-{random_part}"

        # 生成校验码 - 使用固定的密钥算法
        secret_key = "SimpleAuth2024"  # 固定密钥
        checksum_data = f"{card_data}_{days}_{secret_key}"
        checksum = hashlib.md5(checksum_data.encode()).hexdigest()[:4].upper()

        card_key = f"{card_data}-{checksum}"

        return {
            'card_key': card_key,
            'days': days,
            'created_time': timestamp
        }
    
    def validate_card_key(self, card_key):
        """验证卡密格式"""
        try:
            parts = card_key.split('-')
            if len(parts) != 4:
                return False, "卡密格式错误"

            prefix, days_str, random_part, checksum = parts

            # 验证天数
            try:
                days = int(days_str)
            except:
                return False, "天数格式错误"

            # 重新计算校验码 - 使用相同的算法
            card_data = f"{prefix}-{days_str}-{random_part}"
            secret_key = "SimpleAuth2024"  # 相同的固定密钥
            checksum_data = f"{card_data}_{days}_{secret_key}"
            expected_checksum = hashlib.md5(checksum_data.encode()).hexdigest()[:4].upper()

            if checksum != expected_checksum:
                return False, f"卡密校验失败 (期望:{expected_checksum}, 实际:{checksum})"

            # 计算过期时间
            expire_time = int(time.time()) + (days * 24 * 60 * 60)

            return True, {
                'days': days,
                'expire_time': expire_time
            }

        except Exception as e:
            return False, f"验证失败: {e}"
    
    def online_verify(self, card_key):
        """在线验证卡密"""
        try:
            data = {
                'card_key': card_key,
                'machine_id': self.machine_id,
                'app_name': self.app_name,
                'action': 'verify'
            }
            
            response = requests.post(f"{self.server_url}/verify", json=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result.get('success', False), result.get('message', ''), result.get('data', {})
            else:
                return False, "服务器连接失败", {}
                
        except requests.exceptions.RequestException:
            return False, "网络连接失败", {}
        except Exception as e:
            return False, f"验证失败: {e}", {}
    
    def offline_verify(self, card_key):
        """离线验证卡密"""
        # 检查本地授权文件
        if os.path.exists(self.local_auth_file):
            try:
                with open(self.local_auth_file, 'r') as f:
                    auth_data = json.load(f)
                
                # 检查是否过期
                if auth_data.get('expire_time', 0) > time.time():
                    if auth_data.get('machine_id') == self.machine_id:
                        return True, "离线授权有效", auth_data
                    else:
                        return False, "机器ID不匹配", {}
                else:
                    return False, "授权已过期", {}
                    
            except:
                pass
        
        # 验证卡密格式
        valid, result = self.validate_card_key(card_key)
        if not valid:
            return False, result, {}
        
        # 保存授权信息
        auth_data = {
            'card_key': card_key,
            'machine_id': self.machine_id,
            'expire_time': result['expire_time'],
            'activated_time': time.time(),
            'app_name': self.app_name
        }
        
        try:
            with open(self.local_auth_file, 'w') as f:
                json.dump(auth_data, f)
        except:
            pass
        
        return True, "卡密验证成功", auth_data
    
    def verify_authorization(self, card_key=None):
        """验证授权（优先在线，失败则离线）"""
        print(f"🔐 {self.app_name} 授权验证")
        print(f"🖥️ 机器ID: {self.machine_id}")
        
        # 如果没有提供卡密，检查本地授权
        if not card_key:
            if os.path.exists(self.local_auth_file):
                try:
                    with open(self.local_auth_file, 'r') as f:
                        auth_data = json.load(f)
                    
                    if auth_data.get('expire_time', 0) > time.time():
                        if auth_data.get('machine_id') == self.machine_id:
                            remaining_days = int((auth_data['expire_time'] - time.time()) / (24 * 60 * 60))
                            print(f"✅ 本地授权有效，剩余 {remaining_days} 天")
                            return True, "本地授权有效", auth_data
                
                except:
                    pass
            
            return False, "需要输入卡密", {}
        
        # 尝试在线验证
        print("🌐 尝试在线验证...")
        success, message, data = self.online_verify(card_key)
        
        if success:
            print(f"✅ 在线验证成功: {message}")
            return True, message, data
        else:
            print(f"❌ 在线验证失败: {message}")
        
        # 尝试离线验证
        print("💾 尝试离线验证...")
        success, message, data = self.offline_verify(card_key)
        
        if success:
            print(f"✅ 离线验证成功: {message}")
            return True, message, data
        else:
            print(f"❌ 离线验证失败: {message}")
            return False, message, {}
    
    def get_auth_status(self):
        """获取授权状态"""
        if not os.path.exists(self.local_auth_file):
            return False, "未授权", {}
        
        try:
            with open(self.local_auth_file, 'r') as f:
                auth_data = json.load(f)
            
            if auth_data.get('expire_time', 0) > time.time():
                if auth_data.get('machine_id') == self.machine_id:
                    remaining_time = auth_data['expire_time'] - time.time()
                    remaining_days = int(remaining_time / (24 * 60 * 60))
                    
                    return True, f"授权有效，剩余 {remaining_days} 天", auth_data
                else:
                    return False, "机器ID不匹配", {}
            else:
                return False, "授权已过期", {}
                
        except:
            return False, "授权文件损坏", {}

def demo_usage():
    """演示使用方法"""
    print("🔐 简单卡密验证系统演示")
    print("=" * 50)
    
    # 创建授权系统实例
    auth = SimpleAuthSystem("DemoApp")
    
    # 生成演示卡密
    print("\n📋 生成演示卡密:")
    demo_cards = []
    for days in [7, 30, 365]:
        card_info = auth.generate_card_key(days, "DEMO")
        demo_cards.append(card_info)
        print(f"   {days:3d}天: {card_info['card_key']}")
    
    # 检查当前授权状态
    print(f"\n📊 当前授权状态:")
    success, message, data = auth.get_auth_status()
    print(f"   状态: {message}")
    
    # 演示验证过程
    if not success:
        print(f"\n🔑 请输入卡密进行验证:")
        print(f"   (可以使用上面生成的演示卡密)")
        
        # 自动使用第一个演示卡密
        test_card = demo_cards[1]['card_key']  # 30天卡密
        print(f"   使用演示卡密: {test_card}")
        
        success, message, data = auth.verify_authorization(test_card)
        
        if success:
            print(f"✅ 验证成功!")
            print(f"   过期时间: {datetime.fromtimestamp(data['expire_time'])}")
        else:
            print(f"❌ 验证失败: {message}")
    
    print(f"\n💡 集成方法:")
    print(f"   在您的程序开始处调用 auth.verify_authorization()")
    print(f"   根据返回结果决定是否允许程序继续运行")

def create_protected_app_template():
    """创建受保护应用模板"""
    template_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
受保护的应用程序模板
集成了卡密验证系统
"""

from 简单卡密验证系统 import SimpleAuthSystem
import sys

def main():
    """主程序"""
    print("🚀 启动受保护的应用程序...")
    
    # 创建授权系统
    auth = SimpleAuthSystem("YourAppName")
    
    # 检查授权状态
    success, message, data = auth.get_auth_status()
    
    if not success:
        print(f"❌ {message}")
        
        # 要求输入卡密
        card_key = input("请输入卡密: ").strip()
        
        if not card_key:
            print("❌ 未输入卡密，程序退出")
            sys.exit(1)
        
        # 验证卡密
        success, message, data = auth.verify_authorization(card_key)
        
        if not success:
            print(f"❌ 验证失败: {message}")
            sys.exit(1)
    
    print(f"✅ 授权验证成功!")
    print(f"📅 授权到期时间: {datetime.fromtimestamp(data['expire_time'])}")
    
    # 这里放您的实际程序代码
    print("🎉 程序正常运行中...")
    
    # 您的程序逻辑
    your_main_program()

def your_main_program():
    """您的实际程序逻辑"""
    print("这里是您的程序主要功能")
    # 在这里添加您的程序代码

if __name__ == "__main__":
    main()
'''
    
    with open('FBaf2提取/受保护应用模板.py', 'w', encoding='utf-8') as f:
        f.write(template_code)
    
    print("✅ 已创建受保护应用模板: 受保护应用模板.py")

if __name__ == "__main__":
    demo_usage()
    print("\n" + "="*50)
    create_protected_app_template()
