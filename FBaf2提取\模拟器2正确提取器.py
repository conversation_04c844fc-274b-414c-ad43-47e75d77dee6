#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器2正确提取器
基于成功经验的正确提取方法
"""

import os
import json
import subprocess
import re
import base64
import binascii
from datetime import datetime

class Emulator2CorrectExtractor:
    def __init__(self):
        self.device_id = "emulator-5556"
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.results = {
            'emulator': 'emulator-2',
            'account_info': {},
            'totp_keys': [],
            'backup_codes': [],
            'search_methods': []
        }
        
    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            full_command = f'"{self.adb_path}" -s {self.device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            return None
    
    def search_52_char_hex_keys(self):
        """搜索52字符十六进制密钥 - 关键方法"""
        print("🔍 搜索52字符十六进制密钥...")
        print("📋 这是成功提取 cef8e49910ec9d37deba94740ca368bb 的方法")
        
        # 使用与成功案例相同的搜索命令
        search_command = "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null"
        
        print(f"   🔍 执行搜索: {search_command}")
        result = self.execute_adb_command(search_command)
        
        found_keys = []
        
        if result:
            hex_candidates = [line.strip() for line in result.split('\n') if line.strip()]
            print(f"   📊 发现 {len(hex_candidates)} 个52字符十六进制候选")
            
            for hex_key in hex_candidates:
                if len(hex_key) == 52:
                    print(f"      🔍 分析十六进制: {hex_key}")
                    
                    # 转换为Base32
                    base32_key = self.hex_to_base32(hex_key)
                    
                    if base32_key and len(base32_key) == 26:
                        print(f"      ✅ 转换成功: {hex_key} → {base32_key}")
                        
                        # 验证是否为有效的TOTP密钥
                        if self.validate_totp_key(base32_key):
                            key_info = {
                                'key': base32_key,
                                'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                                'source': '52字符十六进制转换',
                                'type': 'TOTP_26_HEX_CONVERTED',
                                'original_hex': hex_key,
                                'confidence': 'HIGH'
                            }
                            found_keys.append(key_info)
                            print(f"         🎉 有效TOTP密钥: {base32_key}")
                        else:
                            print(f"         ❌ 无效TOTP密钥")
                    else:
                        print(f"      ❌ 转换失败")
        else:
            print("   ❌ 未找到52字符十六进制数据")
        
        self.results['search_methods'].append({
            'method': '52字符十六进制搜索',
            'command': search_command,
            'found_count': len(found_keys),
            'success': len(found_keys) > 0
        })
        
        return found_keys
    
    def search_26_char_base32_keys(self):
        """搜索26字符Base32密钥"""
        print("\n🔍 搜索26字符Base32密钥...")
        
        search_command = "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null"
        
        print(f"   🔍 执行搜索: {search_command}")
        result = self.execute_adb_command(search_command)
        
        found_keys = []
        
        if result:
            base32_candidates = [line.strip() for line in result.split('\n') if line.strip()]
            print(f"   📊 发现 {len(base32_candidates)} 个26字符Base32候选")
            
            # 去重
            unique_candidates = list(set(base32_candidates))
            print(f"   📊 去重后: {len(unique_candidates)} 个唯一候选")
            
            for base32_key in unique_candidates:
                if len(base32_key) == 26:
                    print(f"      🔍 验证Base32: {base32_key}")
                    
                    if self.validate_totp_key(base32_key) and self.is_genuine_totp_key(base32_key):
                        key_info = {
                            'key': base32_key,
                            'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                            'source': '26字符Base32直接搜索',
                            'type': 'TOTP_26_DIRECT',
                            'confidence': 'MEDIUM'
                        }
                        found_keys.append(key_info)
                        print(f"         ✅ 有效TOTP密钥: {base32_key}")
                    else:
                        print(f"         ❌ 无效或伪造密钥")
        else:
            print("   ❌ 未找到26字符Base32数据")
        
        self.results['search_methods'].append({
            'method': '26字符Base32搜索',
            'command': search_command,
            'found_count': len(found_keys),
            'success': len(found_keys) > 0
        })
        
        return found_keys
    
    def search_base64_encoded_keys(self):
        """搜索Base64编码的密钥"""
        print("\n🔍 搜索Base64编码密钥...")
        
        search_command = "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{35,40\\}={0,2}' {} \\; 2>/dev/null"
        
        print(f"   🔍 执行搜索: {search_command}")
        result = self.execute_adb_command(search_command)
        
        found_keys = []
        
        if result:
            base64_candidates = [line.strip() for line in result.split('\n') if line.strip()]
            print(f"   📊 发现 {len(base64_candidates)} 个Base64候选")
            
            for b64_key in base64_candidates:
                if 35 <= len(b64_key) <= 40:
                    print(f"      🔍 解码Base64: {b64_key}")
                    
                    base32_key = self.base64_to_base32(b64_key)
                    
                    if base32_key and len(base32_key) == 26:
                        print(f"      ✅ 解码成功: {b64_key} → {base32_key}")
                        
                        if self.validate_totp_key(base32_key):
                            key_info = {
                                'key': base32_key,
                                'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                                'source': 'Base64解码转换',
                                'type': 'TOTP_26_B64_CONVERTED',
                                'original_base64': b64_key,
                                'confidence': 'HIGH'
                            }
                            found_keys.append(key_info)
                            print(f"         🎉 有效TOTP密钥: {base32_key}")
                        else:
                            print(f"         ❌ 无效TOTP密钥")
                    else:
                        print(f"      ❌ 解码失败")
        else:
            print("   ❌ 未找到Base64编码数据")
        
        self.results['search_methods'].append({
            'method': 'Base64编码搜索',
            'command': search_command,
            'found_count': len(found_keys),
            'success': len(found_keys) > 0
        })
        
        return found_keys
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32 - 与成功案例相同的方法"""
        try:
            # 清理十六进制字符串
            hex_clean = hex_string.replace(' ', '').lower()
            
            # 验证是否为有效十六进制
            if not re.match(r'^[0-9a-f]+$', hex_clean):
                return None
            
            # 转换为字节
            bytes_data = binascii.unhexlify(hex_clean)
            
            # 转换为Base32
            base32_string = base64.b32encode(bytes_data).decode('ascii')
            
            # 移除填充字符
            base32_clean = base32_string.rstrip('=')
            
            return base32_clean
            
        except Exception as e:
            return None
    
    def base64_to_base32(self, b64_string):
        """Base64转Base32"""
        try:
            # 添加填充
            padded = b64_string + '=' * (4 - len(b64_string) % 4) % 4
            bytes_data = base64.b64decode(padded)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def validate_totp_key(self, key):
        """验证TOTP密钥有效性"""
        try:
            # 检查长度
            if len(key) != 26:
                return False
            
            # 检查Base32字符
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # 尝试Base32解码
            padded = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded)
            
            # 检查解码后的长度
            if len(decoded) < 10 or len(decoded) > 64:
                return False
            
            return True
            
        except:
            return False
    
    def is_genuine_totp_key(self, key):
        """判断是否为真正的TOTP密钥"""
        try:
            # 排除明显的英文单词和常见模式
            exclude_words = [
                'FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'ANDROID', 'SYSTEM',
                'AMBIENT', 'DIFFUSE', 'STATIC', 'ENVIRONMENT', 'TEXTURE', 'ROTATION',
                'FACTOR', 'SHADING', 'PARAMS', 'FAVORITE', 'MESSENGER', 'CONTACT',
                'ENCODING', 'REDIRECT', 'STANDALONE', 'DIALTONE', 'FBINTERNAL'
            ]
            
            key_upper = key.upper()
            for word in exclude_words:
                if word in key_upper:
                    return False
            
            # 检查字符分布
            char_counts = {}
            for char in key:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            # 如果任何字符出现超过总长度的40%，可能不是真正的密钥
            max_char_ratio = max(char_counts.values()) / len(key)
            if max_char_ratio > 0.4:
                return False
            
            # 检查连续相同字符
            consecutive_count = 1
            max_consecutive = 1
            for i in range(1, len(key)):
                if key[i] == key[i-1]:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 1
            
            # 如果有超过3个连续相同字符，可能不是真正的密钥
            if max_consecutive > 3:
                return False
            
            return True
            
        except:
            return False
    
    def extract_account_info(self):
        """提取账号信息"""
        print("👤 提取账号信息...")
        
        # 搜索用户ID
        user_id_cmd = "find /data/data/com.facebook.katana/ -type f -exec grep -o '[0-9]\\{10,15\\}' {} \\; 2>/dev/null | head -5"
        user_id_result = self.execute_adb_command(user_id_cmd)
        
        user_id = "未知"
        if user_id_result:
            user_ids = [uid.strip() for uid in user_id_result.split('\n') if uid.strip() and len(uid.strip()) >= 10]
            if user_ids:
                user_id = user_ids[0]
                print(f"   ✅ 发现用户ID: {user_id}")
        
        self.results['account_info'] = {
            'emulator': 'emulator-2',
            'user_id': user_id,
            'username': f'Facebook用户_模拟器2',
            'status': '已搜索'
        }
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/emulator2_correct_{timestamp}.json"
        
        os.makedirs("facebook_2fa_results", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def display_results(self):
        """显示结果"""
        print("\n" + "="*60)
        print("📊 模拟器2正确提取结果")
        print("="*60)
        
        # 账号信息
        print("👤 账号信息:")
        for key, value in self.results['account_info'].items():
            print(f"   {key}: {value}")
        
        # TOTP密钥
        if self.results['totp_keys']:
            print(f"\n🔑 TOTP密钥 ({len(self.results['totp_keys'])}个):")
            for i, key in enumerate(self.results['totp_keys'], 1):
                print(f"   {i}. {key['key']}")
                print(f"      格式化: {key['formatted']}")
                print(f"      来源: {key['source']}")
                print(f"      置信度: {key['confidence']}")
                if 'original_hex' in key:
                    print(f"      原始十六进制: {key['original_hex']}")
                if 'original_base64' in key:
                    print(f"      原始Base64: {key['original_base64']}")
                print()
        else:
            print("\n❌ 未发现TOTP密钥")
        
        # 搜索方法摘要
        print("🔍 搜索方法摘要:")
        for method in self.results['search_methods']:
            status = "✅ 成功" if method['success'] else "❌ 失败"
            print(f"   {method['method']}: {status} (找到{method['found_count']}个)")
    
    def run_extraction(self):
        """运行正确的提取流程"""
        print("🎯 模拟器2正确提取器")
        print("基于成功经验的正确提取方法")
        print("="*60)
        
        # 1. 提取账号信息
        self.extract_account_info()
        
        # 2. 搜索52字符十六进制密钥 (最重要的方法)
        hex_keys = self.search_52_char_hex_keys()
        self.results['totp_keys'].extend(hex_keys)
        
        # 3. 搜索26字符Base32密钥
        base32_keys = self.search_26_char_base32_keys()
        self.results['totp_keys'].extend(base32_keys)
        
        # 4. 搜索Base64编码密钥
        base64_keys = self.search_base64_encoded_keys()
        self.results['totp_keys'].extend(base64_keys)
        
        # 5. 去重
        self.deduplicate_keys()
        
        # 6. 显示结果
        self.display_results()
        
        # 7. 保存结果
        filename = self.save_results()
        print(f"\n✅ 提取结果已保存到: {filename}")
        
        return len(self.results['totp_keys']) > 0
    
    def deduplicate_keys(self):
        """去重TOTP密钥"""
        seen_keys = set()
        unique_keys = []
        
        for key_info in self.results['totp_keys']:
            if key_info['key'] not in seen_keys:
                seen_keys.add(key_info['key'])
                unique_keys.append(key_info)
        
        self.results['totp_keys'] = unique_keys

def main():
    """主函数"""
    extractor = Emulator2CorrectExtractor()
    success = extractor.run_extraction()
    
    if success:
        print("\n💡 使用说明:")
        print("1. 复制上面的TOTP密钥")
        print("2. 在验证器应用中添加新账户")
        print("3. 手动输入密钥")
        print("4. 账户名称: Facebook_模拟器2")
        print("5. 开始生成6位验证码")
        print("\n🎯 优先使用置信度为HIGH的密钥！")
    else:
        print("\n💡 建议:")
        print("1. 确保模拟器2正在运行且已Root")
        print("2. 确保Facebook应用已安装并登录")
        print("3. 确保已启用2FA")

if __name__ == "__main__":
    main()
