{"emulator": "emulator-2", "search_results": [{"pattern": "52字符十六进制", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "40字符十六进制", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{40\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "32字符十六进制", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{32\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "26字符Base32", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "32字符Base32", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "20字符Base32", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{20\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "16字符Base32", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{16\\}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "Base64长", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{35,40\\}={0,2}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "Base64中", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{28,34\\}={0,2}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "Base64短", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{20,27\\}={0,2}' {} \\; 2>/dev/null", "total_found": 0, "unique_found": 0, "candidates": []}, {"pattern": "长字母数字", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9]\\{20,32\\}' {} \\; 2>/dev/null", "total_found": 4206, "unique_found": 1620, "candidates": ["Binary file /data/data/com.facebook.katana/lib-compressed/libcdl.so matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/49bc376f7724f652_0 matches", "/data/data/com.facebook.katana/app_overtheair/updates/610790690/613269597/ReactMobileConfigMetadata.json:120", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/3fa6e97adc55728c_0 matches", "Binary file /data/data/com.facebook.katana/files/NewsFeed/80/6c8157d3-b2a9-42ac-9e2f-27b489840a49 matches", "Binary file /data/data/com.facebook.katana/app_msqrd_effect_asset_disk_cache_fixed_sessionless/217802540979786/da784415257106c1e541bea299fc2482.jpg matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/c1593e4bbfda3e52_0 matches", "Binary file /data/data/com.facebook.katana/cache/image_scoped/61562522186148/-Y3UiDYXQbmaZ9plioACv6e5-kw matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/74deb6a9bf295079_0 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/151fd7fe794c78da_0 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/daab94407c501c5b_0 matches", "/data/data/com.facebook.katana/app_overtheair/updates/610790690/613269597/params_map.txt:320", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/da05b512a5954942_0 matches", "Binary file /data/data/com.facebook.katana/lib-compressed/libpando-serialize-jni.so matches", "Binary file /data/data/com.facebook.katana/files/NewsFeed/DB/02b06820-96ad-4f27-9d36-da3c2439af7a matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/ad69570ed965de0d_0 matches", "Binary file /data/data/com.facebook.katana/modules/papaya_aa364068b3f75eb22d17c035a9604d42ddd82dd83a9b4a1ad1641721de0cad76/download.zip matches", "/data/data/com.facebook.katana/files/mobileconfig/sessionless.data/params_map.txt:320", "Binary file /data/data/com.facebook.katana/app_light_prefs/com.facebook.katana/dbl_local_auth_61562522186148 matches", "Binary file /data/data/com.facebook.katana/lib-compressed/librtimerged_1.so matches"]}, {"pattern": "纯数字长", "command": "find /data/data/com.facebook.katana/ -type f -exec grep -o '[0-9]\\{16,32\\}' {} \\; 2>/dev/null", "total_found": 4133, "unique_found": 843, "candidates": ["/data/data/com.facebook.katana/cache/mqtt_log_event1.txt:616", "Binary file /data/data/com.facebook.katana/lib-compressed/libcdl.so matches", "/data/data/com.facebook.katana/cache/mqtt_log_event4.txt:416", "/data/data/com.facebook.katana/app_msqrd_effect_asset_disk_cache_fixed_sessionless/312055958338796/main.json:816", "Binary file /data/data/com.facebook.katana/files/NewsFeed/47/be06d359-5376-486a-ab4c-0b9712fbd2c9 matches", "Binary file /data/data/com.facebook.katana/files/NewsFeed/AC/e50d4bfb-c350-4b68-b158-306cdd5c6f21 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/49bc376f7724f652_0 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/a105eab9077e8961_0 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/fd450683152110fd_0 matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/3fa6e97adc55728c_0 matches", "/data/data/com.facebook.katana/files/mobileconfig/sessionless.data/params_map.txt:516", "Binary file /data/data/com.facebook.katana/files/NewsFeed/80/6c8157d3-b2a9-42ac-9e2f-27b489840a49 matches", "Binary file /data/data/com.facebook.katana/lib-compressed/libxplat_data_compression_dz_dz_facebook_static_dict_1.so matches", "/data/data/com.facebook.katana/files/mobileconfig/61562522186148_aid.data/params_map_kMobileConfigAdminId.txt:816", "Binary file /data/data/com.facebook.katana/cache/ExoPlayerCacheDir/videocache/8/24179885288340244.null.3724145327884244v.-1.AQPLiNUu9jQpGgS1VtY8wa26b4-j0hYV1sfBCDixXm_Rlg4U2g_9J_9rWRxW_b2jKUxlHQMiURijlmto1tjDHEXlViw8VoUSFaq8hP8.mp4.0.1753851058123.v2.exo matches", "Binary file /data/data/com.facebook.katana/app_msqrd_effect_asset_disk_cache_fixed_sessionless/217802540979786/da784415257106c1e541bea299fc2482.jpg matches", "Binary file /data/data/com.facebook.katana/databases/ssus.61562522186148.android_facebook_newsfeed_db matches", "Binary file /data/data/com.facebook.katana/files/NewsFeed/1F/7e3806e6-f1a8-46bc-8081-315819ff950c matches", "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/c1593e4bbfda3e52_0 matches", "/data/data/com.facebook.katana/shared_prefs/rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml:916"]}], "totp_candidates": [], "all_findings": [{"value": "Binary file /data/data/com.facebook.katana/lib-compressed/libcdl.so matches", "length": 75, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/49bc376f7724f652_0 matches", "length": 102, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "/data/data/com.facebook.katana/app_overtheair/updates/610790690/613269597/ReactMobileConfigMetadata.json:120", "length": 108, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/3fa6e97adc55728c_0 matches", "length": 102, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/files/NewsFeed/80/6c8157d3-b2a9-42ac-9e2f-27b489840a49 matches", "length": 105, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/app_msqrd_effect_asset_disk_cache_fixed_sessionless/217802540979786/da784415257106c1e541bea299fc2482.jpg matches", "length": 155, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/c1593e4bbfda3e52_0 matches", "length": 102, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/image_scoped/61562522186148/-Y3UiDYXQbmaZ9plioACv6e5-kw matches", "length": 112, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/74deb6a9bf295079_0 matches", "length": 102, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/151fd7fe794c78da_0 matches", "length": 102, "source_pattern": "长字母数字", "analysis_results": []}, {"value": "/data/data/com.facebook.katana/cache/mqtt_log_event1.txt:616", "length": 60, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/lib-compressed/libcdl.so matches", "length": 75, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "/data/data/com.facebook.katana/cache/mqtt_log_event4.txt:416", "length": 60, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "/data/data/com.facebook.katana/app_msqrd_effect_asset_disk_cache_fixed_sessionless/312055958338796/main.json:816", "length": 112, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/files/NewsFeed/47/be06d359-5376-486a-ab4c-0b9712fbd2c9 matches", "length": 105, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/files/NewsFeed/AC/e50d4bfb-c350-4b68-b158-306cdd5c6f21 matches", "length": 105, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/49bc376f7724f652_0 matches", "length": 102, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/a105eab9077e8961_0 matches", "length": 102, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/fd450683152110fd_0 matches", "length": 102, "source_pattern": "纯数字长", "analysis_results": []}, {"value": "Binary file /data/data/com.facebook.katana/cache/WebView/Default/HTTP Cache/3fa6e97adc55728c_0 matches", "length": 102, "source_pattern": "纯数字长", "analysis_results": []}]}