#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器2深度搜索器
专门搜索模拟器2中的Facebook 2FA信息
"""

import os
import json
import subprocess
import re
from datetime import datetime

class Emulator2DeepSearcher:
    def __init__(self):
        self.emulator_index = 2
        self.results = {
            'emulator': 'emulator-2',
            'connection_status': 'unknown',
            'facebook_installed': False,
            'totp_keys': [],
            'backup_codes': [],
            'raw_data': []
        }
        
        # 雷电模拟器ADB路径
        self.adb_paths = [
            r"G:\leidian\LDPlayer9\adb.exe",
            r"G:\LDPlayer\LDPlayer9\adb.exe",
            r"G:\LDPlayer\LDPlayer4\adb.exe",
            r"C:\LDPlayer\LDPlayer9\adb.exe",
            r"C:\LDPlayer\LDPlayer4\adb.exe",
            r"D:\LDPlayer\LDPlayer9\adb.exe",
            r"D:\LDPlayer\LDPlayer4\adb.exe"
        ]
        
        self.adb_path = self.find_adb()
        
    def find_adb(self):
        """查找ADB路径"""
        for path in self.adb_paths:
            if os.path.exists(path):
                print(f"✅ 找到ADB: {path}")
                return path
        print("❌ 未找到雷电ADB，尝试系统ADB")
        return "adb"  # 使用系统ADB
    
    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            # 模拟器2的设备ID通常是 emulator-5556
            device_id = "emulator-5556"
            full_command = f'"{self.adb_path}" -s {device_id} shell "{command}"'
            
            print(f"🔍 执行命令: {command}")
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                print(f"   ❌ 命令失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"   ❌ 执行异常: {e}")
            return None
    
    def check_emulator_connection(self):
        """检查模拟器连接状态"""
        print("📱 检查模拟器2连接状态...")
        
        try:
            # 检查设备列表
            devices_result = subprocess.run(f'"{self.adb_path}" devices', shell=True, capture_output=True, text=True)

            print(f"🔍 设备列表: {devices_result.stdout}")

            if "emulator-5556" in devices_result.stdout and "device" in devices_result.stdout:
                self.results['connection_status'] = 'connected'
                print("✅ 模拟器2已连接 (emulator-5556)")
                return True
            else:
                self.results['connection_status'] = 'disconnected'
                print("❌ 模拟器2未连接")
                print("💡 请确保模拟器2正在运行")
                return False
                
        except Exception as e:
            print(f"❌ 检查连接失败: {e}")
            return False
    
    def check_facebook_installation(self):
        """检查Facebook是否安装"""
        print("📱 检查Facebook应用...")
        
        result = self.execute_adb_command("pm list packages | grep facebook")
        
        if result and "com.facebook.katana" in result:
            self.results['facebook_installed'] = True
            print("✅ Facebook应用已安装")
            return True
        else:
            self.results['facebook_installed'] = False
            print("❌ Facebook应用未安装")
            return False
    
    def search_facebook_data(self):
        """搜索Facebook数据"""
        print("🔍 搜索Facebook数据目录...")

        # 首先尝试获取Root权限
        root_check = self.execute_adb_command("su -c 'id'")
        if root_check and "uid=0" in root_check:
            print("✅ Root权限已获取")

            # 使用Root权限检查Facebook数据目录
            data_check = self.execute_adb_command("su -c 'ls -la /data/data/com.facebook.katana/'")

            if data_check and "total" in data_check:
                print("✅ Facebook数据目录存在且可访问")
                print(f"📁 目录内容预览:")
                for line in data_check.split('\n')[:5]:  # 显示前5行
                    if line.strip():
                        print(f"   {line.strip()}")
                return True
            else:
                print("❌ Facebook数据目录不存在或为空")
                return False
        else:
            print("❌ 无法获取Root权限")
            print("💡 请确保模拟器已正确Root")
            return False
    
    def deep_search_totp_keys(self):
        """深度搜索TOTP密钥"""
        print("🔑 深度搜索TOTP密钥...")
        
        search_commands = [
            # 搜索26字符Base32 (使用Root权限)
            "su -c 'find /data/data/com.facebook.katana/ -type f -exec grep -o \"[A-Z2-7]\\{26\\}\" {} \\; 2>/dev/null'",

            # 搜索52字符十六进制 (使用Root权限)
            "su -c 'find /data/data/com.facebook.katana/ -type f -exec grep -o \"[a-fA-F0-9]\\{52\\}\" {} \\; 2>/dev/null'",

            # 搜索数据库文件 (使用Root权限)
            "su -c 'find /data/data/com.facebook.katana/ -name \"*.db\" -exec strings {} \\; 2>/dev/null | grep -o \"[A-Z2-7]\\{26\\}\"'",

            # 搜索XML文件 (使用Root权限)
            "su -c 'find /data/data/com.facebook.katana/ -name \"*.xml\" -exec cat {} \\; 2>/dev/null | grep -o \"[A-Z2-7]\\{26\\}\"'",

            # 搜索所有文件中的长字符串 (使用Root权限)
            "su -c 'find /data/data/com.facebook.katana/ -type f -exec strings {} \\; 2>/dev/null | grep -o \"[A-Z2-7]\\{20,32\\}\"'"
        ]
        
        found_keys = []
        
        for i, cmd in enumerate(search_commands, 1):
            print(f"   🔍 搜索方法 {i}/5...")
            result = self.execute_adb_command(cmd)
            
            if result:
                candidates = list(set(result.strip().split('\n')))
                for candidate in candidates:
                    if candidate and len(candidate) >= 20:
                        if self.validate_totp_candidate(candidate):
                            key_info = {
                                'key': candidate,
                                'length': len(candidate),
                                'formatted': ' '.join([candidate[i:i+4] for i in range(0, len(candidate), 4)]),
                                'search_method': f'方法{i}',
                                'type': 'TOTP_CANDIDATE'
                            }
                            found_keys.append(key_info)
                            print(f"      ✅ 发现候选密钥: {candidate}")
        
        self.results['totp_keys'] = found_keys
        return found_keys
    
    def validate_totp_candidate(self, candidate):
        """验证TOTP候选密钥"""
        try:
            # 检查长度
            if len(candidate) < 20 or len(candidate) > 32:
                return False
            
            # 检查Base32字符
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not all(c in valid_chars for c in candidate):
                return False
            
            # 尝试Base32解码
            import base64
            padded = candidate + '=' * (8 - len(candidate) % 8) % 8
            base64.b32decode(padded)
            
            return True
            
        except:
            return False
    
    def search_backup_codes(self):
        """搜索备份代码"""
        print("🎫 搜索备份代码...")
        
        backup_commands = [
            # 搜索8位数字
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[0-9]\\{8\\}' {} \\; 2>/dev/null",
            
            # 搜索8位十六进制
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-f0-9]\\{8\\}' {} \\; 2>/dev/null",
            
            # 搜索混合字符
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z0-9]\\{8,12\\}' {} \\; 2>/dev/null"
        ]
        
        found_codes = []
        
        for cmd in backup_commands:
            result = self.execute_adb_command(cmd)
            if result:
                codes = list(set(result.strip().split('\n')))
                for code in codes:
                    if code and len(code) >= 8:
                        # 过滤明显不是备份代码的内容
                        if not any(x in code.lower() for x in ['facebook', 'android', 'google', 'http', 'www']):
                            code_info = {
                                'code': code,
                                'length': len(code),
                                'type': f'{len(code)}位备份代码'
                            }
                            found_codes.append(code_info)
        
        # 去重并限制数量
        unique_codes = []
        seen = set()
        for code in found_codes:
            if code['code'] not in seen:
                seen.add(code['code'])
                unique_codes.append(code)
                if len(unique_codes) >= 20:  # 限制20个
                    break
        
        self.results['backup_codes'] = unique_codes
        print(f"✅ 发现 {len(unique_codes)} 个可能的备份代码")
        
        return unique_codes
    
    def save_results(self):
        """保存搜索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/emulator2_deep_search_{timestamp}.json"
        
        os.makedirs("facebook_2fa_results", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def display_results(self):
        """显示搜索结果"""
        print("\n" + "="*60)
        print("📊 模拟器2深度搜索结果")
        print("="*60)
        
        print(f"📱 模拟器状态: {self.results['connection_status']}")
        print(f"📱 Facebook安装: {'是' if self.results['facebook_installed'] else '否'}")
        
        if self.results['totp_keys']:
            print(f"\n🔑 发现的TOTP密钥 ({len(self.results['totp_keys'])}个):")
            for i, key in enumerate(self.results['totp_keys'], 1):
                print(f"   {i}. {key['key']}")
                print(f"      长度: {key['length']} 字符")
                print(f"      格式化: {key['formatted']}")
                print(f"      搜索方法: {key['search_method']}")
                print()
        else:
            print("\n❌ 未发现TOTP密钥")
        
        if self.results['backup_codes']:
            print(f"🎫 发现的备份代码 ({len(self.results['backup_codes'])}个):")
            for i, code in enumerate(self.results['backup_codes'][:10], 1):  # 只显示前10个
                print(f"   {i}. {code['code']} ({code['type']})")
        else:
            print("\n❌ 未发现备份代码")
    
    def run_deep_search(self):
        """运行深度搜索"""
        print("🎯 模拟器2深度搜索器")
        print("="*50)
        
        # 1. 检查连接
        if not self.check_emulator_connection():
            return False
        
        # 2. 检查Facebook安装
        if not self.check_facebook_installation():
            return False
        
        # 3. 检查数据目录
        if not self.search_facebook_data():
            return False
        
        # 4. 搜索TOTP密钥
        self.deep_search_totp_keys()
        
        # 5. 搜索备份代码
        self.search_backup_codes()
        
        # 6. 显示结果
        self.display_results()
        
        # 7. 保存结果
        filename = self.save_results()
        print(f"\n✅ 搜索结果已保存到: {filename}")
        
        return True

def main():
    """主函数"""
    searcher = Emulator2DeepSearcher()
    success = searcher.run_deep_search()
    
    if not success:
        print("\n💡 解决建议:")
        print("1. 确保模拟器2正在运行")
        print("2. 确保Facebook应用已安装并登录")
        print("3. 确保模拟器已获得Root权限")
        print("4. 尝试重启模拟器2")

if __name__ == "__main__":
    main()
