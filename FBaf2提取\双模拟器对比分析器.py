#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双模拟器对比分析器
同时分析两个模拟器，找出为什么只有一个有TOTP密钥
"""

import os
import json
import subprocess
import re
from datetime import datetime

class DualEmulatorAnalyzer:
    def __init__(self):
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.emulators = {
            'emulator1': {
                'device_id': '127.0.0.1:5557',
                'name': '模拟器1',
                'data': {}
            },
            'emulator2': {
                'device_id': 'emulator-5556',
                'name': '模拟器2',
                'data': {}
            }
        }
        
    def execute_adb_command(self, device_id, command):
        """执行ADB命令"""
        try:
            full_command = f'"{self.adb_path}" -s {device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            return None
    
    def analyze_facebook_installation(self):
        """分析Facebook安装情况"""
        print("📱 分析Facebook安装情况")
        print("=" * 50)
        
        for emu_key, emu_info in self.emulators.items():
            device_id = emu_info['device_id']
            name = emu_info['name']
            
            print(f"\n🔍 {name} ({device_id}):")
            
            # 检查Facebook是否安装
            package_check = self.execute_adb_command(device_id, "pm list packages | grep facebook")
            
            if package_check and "com.facebook.katana" in package_check:
                print("   ✅ Facebook应用已安装")
                
                # 获取应用详细信息
                app_info = self.execute_adb_command(device_id, "dumpsys package com.facebook.katana | grep -E 'versionName|versionCode|firstInstallTime|lastUpdateTime'")
                
                if app_info:
                    print("   📊 应用信息:")
                    for line in app_info.split('\n'):
                        if line.strip():
                            print(f"      {line.strip()}")
                
                # 检查数据目录
                data_dir_check = self.execute_adb_command(device_id, "ls -la /data/data/com.facebook.katana/")
                
                if data_dir_check:
                    print("   ✅ 数据目录可访问")
                    emu_info['data']['facebook_installed'] = True
                    emu_info['data']['data_accessible'] = True
                else:
                    print("   ❌ 数据目录不可访问")
                    emu_info['data']['facebook_installed'] = True
                    emu_info['data']['data_accessible'] = False
                    
            else:
                print("   ❌ Facebook应用未安装")
                emu_info['data']['facebook_installed'] = False
                emu_info['data']['data_accessible'] = False
    
    def analyze_user_accounts(self):
        """分析用户账号信息"""
        print("\n👤 分析用户账号信息")
        print("=" * 50)
        
        for emu_key, emu_info in self.emulators.items():
            device_id = emu_info['device_id']
            name = emu_info['name']
            
            print(f"\n🔍 {name} ({device_id}):")
            
            if not emu_info['data'].get('data_accessible', False):
                print("   ❌ 无法访问数据，跳过分析")
                continue
            
            # 获取用户ID
            user_id = self.execute_adb_command(device_id, "cat /data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml | grep -o '[0-9]\\{10,15\\}' | head -1")
            
            if user_id:
                print(f"   ✅ 用户ID: {user_id}")
                emu_info['data']['user_id'] = user_id
            else:
                print("   ❌ 未找到用户ID")
                emu_info['data']['user_id'] = None
            
            # 检查登录状态
            auth_data = self.execute_adb_command(device_id, "cat /data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml")
            
            if auth_data and len(auth_data) > 100:
                print("   ✅ 有认证数据 (已登录)")
                emu_info['data']['logged_in'] = True
                
                # 检查认证数据大小
                print(f"   📊 认证数据大小: {len(auth_data)} 字符")
                emu_info['data']['auth_data_size'] = len(auth_data)
            else:
                print("   ❌ 无认证数据 (未登录)")
                emu_info['data']['logged_in'] = False
                emu_info['data']['auth_data_size'] = 0
    
    def search_totp_keys_detailed(self):
        """详细搜索TOTP密钥"""
        print("\n🔑 详细搜索TOTP密钥")
        print("=" * 50)
        
        search_patterns = [
            ("52字符十六进制", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null"),
            ("40字符十六进制", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{40\\}' {} \\; 2>/dev/null"),
            ("26字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null"),
            ("32字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null"),
        ]
        
        for emu_key, emu_info in self.emulators.items():
            device_id = emu_info['device_id']
            name = emu_info['name']
            
            print(f"\n🔍 {name} ({device_id}):")
            
            if not emu_info['data'].get('data_accessible', False):
                print("   ❌ 无法访问数据，跳过搜索")
                continue
            
            emu_info['data']['totp_search_results'] = {}
            
            for pattern_name, command in search_patterns:
                print(f"   🔍 搜索 {pattern_name}...")
                
                result = self.execute_adb_command(device_id, command)
                
                if result:
                    candidates = [line.strip() for line in result.split('\n') if line.strip()]
                    unique_candidates = list(set(candidates))
                    
                    print(f"      ✅ 找到 {len(unique_candidates)} 个唯一候选")
                    emu_info['data']['totp_search_results'][pattern_name] = unique_candidates
                    
                    # 显示前3个候选
                    for i, candidate in enumerate(unique_candidates[:3], 1):
                        print(f"         {i}. {candidate}")
                    
                    if len(unique_candidates) > 3:
                        print(f"         ... 还有 {len(unique_candidates) - 3} 个")
                        
                else:
                    print(f"      ❌ 未找到")
                    emu_info['data']['totp_search_results'][pattern_name] = []
    
    def analyze_database_files(self):
        """分析数据库文件"""
        print("\n🗄️ 分析数据库文件")
        print("=" * 50)
        
        for emu_key, emu_info in self.emulators.items():
            device_id = emu_info['device_id']
            name = emu_info['name']
            
            print(f"\n🔍 {name} ({device_id}):")
            
            if not emu_info['data'].get('data_accessible', False):
                print("   ❌ 无法访问数据，跳过分析")
                continue
            
            # 检查prefs_db文件
            prefs_db_check = self.execute_adb_command(device_id, "ls -la /data/data/com.facebook.katana/databases/prefs_db")
            
            if prefs_db_check:
                print("   ✅ prefs_db 文件存在")
                
                # 获取文件大小
                file_size = prefs_db_check.split()[4] if len(prefs_db_check.split()) > 4 else "未知"
                print(f"      文件大小: {file_size} 字节")
                
                # 在prefs_db中搜索十六进制密钥
                hex_in_db = self.execute_adb_command(device_id, "strings /data/data/com.facebook.katana/databases/prefs_db | grep -o '[a-fA-F0-9]\\{40,52\\}' | head -5")
                
                if hex_in_db:
                    print("   ✅ 在prefs_db中找到十六进制数据:")
                    for line in hex_in_db.split('\n'):
                        if line.strip():
                            print(f"      🔑 {line.strip()}")
                    emu_info['data']['prefs_db_has_hex'] = True
                else:
                    print("   ❌ prefs_db中未找到十六进制密钥")
                    emu_info['data']['prefs_db_has_hex'] = False
                    
            else:
                print("   ❌ prefs_db 文件不存在")
                emu_info['data']['prefs_db_exists'] = False
    
    def compare_results(self):
        """对比分析结果"""
        print("\n📊 对比分析结果")
        print("=" * 60)
        
        emu1_data = self.emulators['emulator1']['data']
        emu2_data = self.emulators['emulator2']['data']
        
        print("🔍 关键差异分析:")
        print("-" * 40)
        
        # 对比Facebook安装状态
        print(f"📱 Facebook安装:")
        print(f"   模拟器1: {'✅ 已安装' if emu1_data.get('facebook_installed') else '❌ 未安装'}")
        print(f"   模拟器2: {'✅ 已安装' if emu2_data.get('facebook_installed') else '❌ 未安装'}")
        
        # 对比数据访问权限
        print(f"🔐 数据访问:")
        print(f"   模拟器1: {'✅ 可访问' if emu1_data.get('data_accessible') else '❌ 不可访问'}")
        print(f"   模拟器2: {'✅ 可访问' if emu2_data.get('data_accessible') else '❌ 不可访问'}")
        
        # 对比用户ID
        print(f"👤 用户ID:")
        print(f"   模拟器1: {emu1_data.get('user_id', '未知')}")
        print(f"   模拟器2: {emu2_data.get('user_id', '未知')}")
        
        # 对比登录状态
        print(f"🔑 登录状态:")
        print(f"   模拟器1: {'✅ 已登录' if emu1_data.get('logged_in') else '❌ 未登录'}")
        print(f"   模拟器2: {'✅ 已登录' if emu2_data.get('logged_in') else '❌ 未登录'}")
        
        # 对比认证数据大小
        print(f"📊 认证数据大小:")
        print(f"   模拟器1: {emu1_data.get('auth_data_size', 0)} 字符")
        print(f"   模拟器2: {emu2_data.get('auth_data_size', 0)} 字符")
        
        # 对比TOTP搜索结果
        print(f"🔑 TOTP密钥搜索结果:")
        
        if 'totp_search_results' in emu1_data and 'totp_search_results' in emu2_data:
            for pattern in ["52字符十六进制", "40字符十六进制", "26字符Base32", "32字符Base32"]:
                emu1_count = len(emu1_data['totp_search_results'].get(pattern, []))
                emu2_count = len(emu2_data['totp_search_results'].get(pattern, []))
                
                print(f"   {pattern}:")
                print(f"      模拟器1: {emu1_count} 个")
                print(f"      模拟器2: {emu2_count} 个")
        
        # 对比prefs_db
        print(f"🗄️ prefs_db数据库:")
        print(f"   模拟器1: {'✅ 有十六进制数据' if emu1_data.get('prefs_db_has_hex') else '❌ 无十六进制数据'}")
        print(f"   模拟器2: {'✅ 有十六进制数据' if emu2_data.get('prefs_db_has_hex') else '❌ 无十六进制数据'}")
        
        # 分析原因
        print(f"\n🎯 可能的原因分析:")
        print("-" * 40)
        
        if emu1_data.get('user_id') == emu2_data.get('user_id'):
            print("✅ 两个模拟器使用相同的Facebook账号")
            
            if emu1_data.get('prefs_db_has_hex') and not emu2_data.get('prefs_db_has_hex'):
                print("🔍 关键发现: 模拟器1有TOTP数据，模拟器2没有")
                print("💡 可能原因:")
                print("   1. 模拟器1在启用2FA后使用过")
                print("   2. 模拟器2是在启用2FA之前的状态")
                print("   3. 数据同步问题")
                print("   4. 不同的Facebook应用版本")
            
        else:
            print("❌ 两个模拟器使用不同的Facebook账号")
            print("💡 这解释了为什么TOTP密钥不同")
    
    def save_analysis_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/dual_emulator_analysis_{timestamp}.json"
        
        os.makedirs("facebook_2fa_results", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.emulators, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def run_analysis(self):
        """运行完整分析"""
        print("🎯 双模拟器对比分析器")
        print("分析为什么只有一个模拟器有TOTP密钥")
        print("=" * 60)
        
        # 1. 分析Facebook安装情况
        self.analyze_facebook_installation()
        
        # 2. 分析用户账号信息
        self.analyze_user_accounts()
        
        # 3. 搜索TOTP密钥
        self.search_totp_keys_detailed()
        
        # 4. 分析数据库文件
        self.analyze_database_files()
        
        # 5. 对比结果
        self.compare_results()
        
        # 6. 保存结果
        filename = self.save_analysis_results()
        print(f"\n✅ 分析结果已保存到: {filename}")

def main():
    """主函数"""
    analyzer = DualEmulatorAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
