#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化独立Facebook 2FA提取器
彻底解决数据共享问题
"""

import os
import json
import subprocess
import base64
import binascii
import hashlib
from datetime import datetime

class SimplifiedIndependentExtractor:
    def __init__(self):
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        
        # 验证ADB路径
        if not os.path.exists(self.adb_path):
            print(f"❌ ADB路径不存在: {self.adb_path}")
            # 尝试其他可能的路径
            possible_paths = [
                r"C:\LDPlayer\LDPlayer9\adb.exe",
                r"D:\LDPlayer\LDPlayer9\adb.exe",
                "adb"  # 系统PATH中的adb
            ]
            for path in possible_paths:
                if os.path.exists(path) or path == "adb":
                    self.adb_path = path
                    print(f"✅ 使用ADB路径: {path}")
                    break
        
        self.results = {}
        print(f"🆕 独立会话ID: {self.session_id}")
    
    def execute_adb(self, device_id, command):
        """执行ADB命令"""
        try:
            full_cmd = f'"{self.adb_path}" -s {device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
        except Exception as e:
            print(f"命令执行错误: {e}")
            return None
    
    def get_devices(self):
        """获取设备列表"""
        try:
            result = subprocess.run(f'"{self.adb_path}" devices', shell=True, capture_output=True, text=True)
            
            devices = []
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                for line in lines:
                    if line.strip() and 'device' in line:
                        device_id = line.split()[0]
                        devices.append(device_id)
            
            return devices
        except Exception as e:
            print(f"获取设备列表失败: {e}")
            return []
    
    def get_facebook_user_id(self, device_id):
        """获取Facebook用户ID"""
        print(f"  📱 获取设备 {device_id} 的用户ID...")
        
        # 启动Facebook应用确保数据加载
        self.execute_adb(device_id, "am start -n com.facebook.katana/.LoginActivity")
        
        import time
        time.sleep(2)
        
        # 多种方法获取用户ID
        methods = [
            "cat /data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml | grep -o '[0-9]\\{10,15\\}' | head -1",
            "strings /data/data/com.facebook.katana/databases/prefs_db | grep -o '[0-9]\\{10,15\\}' | head -1"
        ]
        
        for method in methods:
            result = self.execute_adb(device_id, method)
            if result and result.isdigit() and len(result) >= 10:
                print(f"    ✅ 用户ID: {result}")
                return result
        
        print(f"    ❌ 无法获取用户ID")
        return None
    
    def extract_totp_keys_fresh(self, device_id, user_id):
        """全新提取TOTP密钥"""
        print(f"  🔑 全新提取TOTP密钥...")
        
        # 重新启动Facebook应用
        self.execute_adb(device_id, "am force-stop com.facebook.katana")
        import time
        time.sleep(1)
        self.execute_adb(device_id, "am start -n com.facebook.katana/.LoginActivity")
        time.sleep(3)
        
        found_keys = []
        
        # 方法1: 从prefs_db提取十六进制
        print("    🔍 从数据库提取十六进制...")
        hex_result = self.execute_adb(device_id, "strings /data/data/com.facebook.katana/databases/prefs_db | grep -o '[a-fA-F0-9]\\{40,52\\}'")
        
        if hex_result:
            hex_keys = list(set(hex_result.split('\n')))
            print(f"    📊 发现 {len(hex_keys)} 个十六进制候选")
            
            for hex_key in hex_keys:
                if hex_key.strip() and len(hex_key.strip()) in [40, 52]:
                    base32_key = self.hex_to_base32(hex_key.strip())
                    if base32_key and self.is_valid_totp(base32_key):
                        key_info = {
                            'device_id': device_id,
                            'user_id': user_id,
                            'base32_key': base32_key,
                            'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                            'original_hex': hex_key.strip(),
                            'length': len(base32_key),
                            'method': 'hex_conversion',
                            'session_id': self.session_id
                        }
                        found_keys.append(key_info)
                        print(f"      ✅ 转换成功: {hex_key.strip()} → {base32_key}")
        
        # 方法2: 直接搜索Base32
        print("    🔍 直接搜索Base32格式...")
        base32_result = self.execute_adb(device_id, "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{16,32\\}' {} \\; 2>/dev/null")
        
        if base32_result:
            base32_keys = list(set(base32_result.split('\n')))
            print(f"    📊 发现 {len(base32_keys)} 个Base32候选")
            
            for base32_key in base32_keys:
                if base32_key.strip() and self.is_valid_totp(base32_key.strip()):
                    # 检查是否已存在
                    if not any(k['base32_key'] == base32_key.strip() for k in found_keys):
                        key_info = {
                            'device_id': device_id,
                            'user_id': user_id,
                            'base32_key': base32_key.strip(),
                            'formatted': ' '.join([base32_key.strip()[i:i+4] for i in range(0, len(base32_key.strip()), 4)]),
                            'length': len(base32_key.strip()),
                            'method': 'direct_base32',
                            'session_id': self.session_id
                        }
                        found_keys.append(key_info)
                        print(f"      ✅ 直接发现: {base32_key.strip()}")
        
        print(f"    📊 总共提取到 {len(found_keys)} 个有效TOTP密钥")
        return found_keys
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            bytes_data = binascii.unhexlify(hex_string)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def is_valid_totp(self, key):
        """验证TOTP密钥"""
        try:
            if len(key) < 16 or len(key) > 32:
                return False
            
            # 字符检查
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # Base32解码测试
            padding = (8 - len(key) % 8) % 8
            padded = key + '=' * padding
            base64.b32decode(padded)
            
            # 排除明显的非密钥
            exclude = ['FACEBOOK', 'GOOGLE', 'ANDROID', 'AAAAAAA', 'BBBBBBB']
            for pattern in exclude:
                if pattern in key.upper():
                    return False
            
            return True
        except:
            return False
    
    def run_extraction(self):
        """运行提取"""
        print("🚀 简化独立Facebook 2FA提取器")
        print("=" * 50)
        
        # 获取设备
        devices = self.get_devices()
        
        if not devices:
            print("❌ 未发现设备")
            return
        
        print(f"📱 发现 {len(devices)} 个设备: {devices}")
        
        # 分析每个设备
        for device_id in devices:
            print(f"\n🔍 分析设备: {device_id}")
            
            # 检查Facebook
            fb_check = self.execute_adb(device_id, "pm list packages | grep facebook")
            if not fb_check or "com.facebook.katana" not in fb_check:
                print("  ❌ Facebook未安装")
                continue
            
            print("  ✅ Facebook已安装")
            
            # 检查数据访问
            data_check = self.execute_adb(device_id, "ls /data/data/com.facebook.katana/")
            if not data_check:
                print("  ❌ 无法访问Facebook数据")
                continue
            
            print("  ✅ Facebook数据可访问")
            
            # 获取用户ID
            user_id = self.get_facebook_user_id(device_id)
            if not user_id:
                print("  ❌ 无法获取用户ID")
                continue
            
            # 提取TOTP密钥
            totp_keys = self.extract_totp_keys_fresh(device_id, user_id)
            
            # 保存结果
            self.results[device_id] = {
                'user_id': user_id,
                'totp_keys': totp_keys,
                'session_id': self.session_id
            }
        
        # 显示结果
        self.display_results()
        
        # 保存到文件
        self.save_results()
    
    def display_results(self):
        """显示结果"""
        print("\n" + "=" * 60)
        print("📊 独立提取结果")
        print("=" * 60)
        
        for device_id, data in self.results.items():
            print(f"\n📱 设备: {device_id}")
            print(f"👤 用户ID: {data['user_id']}")
            print(f"🆔 会话ID: {data['session_id']}")
            
            if data['totp_keys']:
                print(f"🔑 TOTP密钥 ({len(data['totp_keys'])}个):")
                for i, key in enumerate(data['totp_keys'], 1):
                    print(f"  {i}. {key['base32_key']}")
                    print(f"     格式化: {key['formatted']}")
                    print(f"     长度: {key['length']} 字符")
                    print(f"     提取方法: {key['method']}")
                    if 'original_hex' in key:
                        print(f"     原始十六进制: {key['original_hex']}")
                    print()
            else:
                print("❌ 未找到TOTP密钥")
        
        # 对比分析
        if len(self.results) > 1:
            print("\n🔍 设备对比分析:")
            print("-" * 40)
            
            device_ids = list(self.results.keys())
            user_ids = [self.results[d]['user_id'] for d in device_ids]
            
            if len(set(user_ids)) == 1:
                print("⚠️ 警告: 所有设备使用相同的用户ID")
                print("   这可能表示数据共享或克隆问题")
            else:
                print("✅ 设备使用不同的用户ID")
                
                # 检查TOTP密钥是否相同
                all_keys = []
                for device_id, data in self.results.items():
                    for key in data['totp_keys']:
                        all_keys.append(key['base32_key'])
                
                if len(set(all_keys)) < len(all_keys):
                    print("⚠️ 警告: 发现重复的TOTP密钥")
                    print("   不同用户不应该有相同的TOTP密钥")
                else:
                    print("✅ 所有TOTP密钥都是唯一的")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"独立提取结果_{self.session_id}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到: {filename}")

def main():
    extractor = SimplifiedIndependentExtractor()
    extractor.run_extraction()

if __name__ == "__main__":
    main()
