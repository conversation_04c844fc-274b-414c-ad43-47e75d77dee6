#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试UI元素检查功能
"""

import uiautomator2 as u2
import xml.etree.ElementTree as ET
import re

def debug_element_inspection(x, y):
    """调试指定坐标的元素检查"""
    print(f"🔍 调试坐标 ({x}, {y}) 处的元素检查...")
    
    try:
        # 连接设备
        d = u2.connect('127.0.0.1:5557')
        print(f"✅ 设备连接成功")
        
        # 获取UI层次结构
        print("📱 获取UI层次结构...")
        xml_content = d.dump_hierarchy()
        print(f"✅ XML内容长度: {len(xml_content)} 字符")
        
        # 保存XML到文件用于调试
        with open("ui_hierarchy_debug.xml", "w", encoding="utf-8") as f:
            f.write(xml_content)
        print("✅ UI层次结构已保存到 ui_hierarchy_debug.xml")
        
        # 解析XML
        root = ET.fromstring(xml_content)
        print(f"✅ XML解析成功，根元素: {root.tag}")
        
        # 查找所有有bounds的元素
        all_elements_with_bounds = []
        
        def collect_elements_with_bounds(element, depth=0):
            bounds = element.get('bounds', '')
            if bounds:
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    all_elements_with_bounds.append({
                        'depth': depth,
                        'bounds': bounds,
                        'coordinates': (x1, y1, x2, y2),
                        'class': element.get('class', ''),
                        'text': element.get('text', ''),
                        'resource-id': element.get('resource-id', ''),
                        'clickable': element.get('clickable', 'false'),
                        'element': element
                    })
            
            for child in element:
                collect_elements_with_bounds(child, depth + 1)
        
        collect_elements_with_bounds(root)
        print(f"📊 找到 {len(all_elements_with_bounds)} 个有边界的元素")
        
        # 查找包含目标坐标的元素
        matching_elements = []
        for elem_info in all_elements_with_bounds:
            x1, y1, x2, y2 = elem_info['coordinates']
            if x1 <= x <= x2 and y1 <= y <= y2:
                matching_elements.append(elem_info)
        
        print(f"🎯 找到 {len(matching_elements)} 个包含坐标 ({x}, {y}) 的元素:")
        
        for i, elem in enumerate(matching_elements):
            print(f"\n元素 {i+1} (深度 {elem['depth']}):")
            print(f"  边界: {elem['bounds']}")
            print(f"  类名: {elem['class']}")
            print(f"  文本: '{elem['text']}'")
            print(f"  资源ID: {elem['resource-id']}")
            print(f"  可点击: {elem['clickable']}")
        
        # 找到最深层的元素（最精确的匹配）
        if matching_elements:
            deepest_element = max(matching_elements, key=lambda x: x['depth'])
            print(f"\n🎯 最精确匹配 (深度 {deepest_element['depth']}):")
            
            element = deepest_element['element']
            result = {
                'bounds': element.get('bounds', ''),
                'class': element.get('class', ''),
                'text': element.get('text', ''),
                'resource-id': element.get('resource-id', ''),
                'content-desc': element.get('content-desc', ''),
                'checkable': element.get('checkable', 'false'),
                'checked': element.get('checked', 'false'),
                'clickable': element.get('clickable', 'false'),
                'enabled': element.get('enabled', 'false'),
                'focusable': element.get('focusable', 'false'),
                'focused': element.get('focused', 'false'),
                'scrollable': element.get('scrollable', 'false'),
                'long-clickable': element.get('long-clickable', 'false'),
                'password': element.get('password', 'false'),
                'selected': element.get('selected', 'false'),
                'visible-to-user': element.get('visible-to-user', 'false'),
                'package': element.get('package', ''),
                'activity': get_current_activity(d)
            }
            
            print("📋 完整元素信息:")
            for key, value in result.items():
                if value:
                    print(f"  {key}: {value}")
            
            return result
        else:
            print("❌ 未找到包含该坐标的元素")
            
            # 显示最近的元素
            print("\n🔍 显示最近的5个元素:")
            distances = []
            for elem_info in all_elements_with_bounds[:20]:  # 只检查前20个元素
                x1, y1, x2, y2 = elem_info['coordinates']
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                distance = ((center_x - x) ** 2 + (center_y - y) ** 2) ** 0.5
                distances.append((distance, elem_info))
            
            distances.sort(key=lambda x: x[0])
            for i, (dist, elem) in enumerate(distances[:5]):
                x1, y1, x2, y2 = elem['coordinates']
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                print(f"  {i+1}. 距离 {dist:.1f} - 中心({center_x},{center_y}) - {elem['class']} - '{elem['text']}'")
            
            return None
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_current_activity(device):
    """获取当前Activity"""
    try:
        info = device.app_current()
        return f"{info.get('package', '')}/{info.get('activity', '')}"
    except:
        return ""

def test_multiple_coordinates():
    """测试多个坐标点"""
    test_points = [
        (130, 200),  # 屏幕中心附近
        (50, 50),    # 左上角
        (200, 100),  # 右上角
        (100, 300),  # 下方
        (17, 32),    # 从之前测试中的坐标
        (219, 32),   # 另一个测试坐标
    ]
    
    print("🧪 测试多个坐标点...")
    for x, y in test_points:
        print(f"\n{'='*50}")
        result = debug_element_inspection(x, y)
        if result:
            print(f"✅ 坐标 ({x}, {y}) 找到元素")
        else:
            print(f"❌ 坐标 ({x}, {y}) 未找到元素")

if __name__ == "__main__":
    print("🚀 开始UI元素检查调试...")
    
    # 测试特定坐标
    debug_element_inspection(130, 200)
    
    # 测试多个坐标
    test_multiple_coordinates()
