#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简版Facebook 2FA提取器
只输出账号和2FA信息，无其他冗余信息
"""

import os
import json
import subprocess
from datetime import datetime

class SimpleFB2FAExtractor:
    def __init__(self, emulator_index=1):
        self.emulator_index = emulator_index
        self.results = {
            'account_info': {},
            'totp_keys': [],
            'backup_codes': []
        }
        print(f"🎯 目标模拟器: 模拟器{emulator_index}")
        
    def execute_command(self, command):
        """执行系统命令"""
        try:
            # 根据模拟器索引调整命令
            if self.emulator_index > 1:
                # 对于模拟器2及以上，需要通过ADB连接
                adb_command = f"adb -s emulator-{5554 + (self.emulator_index-1) * 2} shell \"{command}\""
                result = subprocess.run(adb_command, shell=True, capture_output=True, text=True, timeout=30)
            else:
                # 模拟器1直接执行
                result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout if result.returncode == 0 else None
        except:
            return None
    
    def extract_account_info(self):
        """提取账号信息"""
        print("🔍 提取账号信息...")

        # 尝试从模拟器中提取真实的账号信息
        user_id = self.search_user_id()

        account_data = {
            'emulator': f'模拟器{self.emulator_index}',
            'user_id': user_id if user_id else f'模拟器{self.emulator_index}_用户',
            'username': f'Facebook用户_模拟器{self.emulator_index}',
            'status': '搜索中...'
        }

        self.results['account_info'] = account_data
        print(f"✅ 模拟器: {account_data['emulator']}")
        print(f"✅ 账号ID: {account_data['user_id']}")

    def search_user_id(self):
        """搜索用户ID"""
        try:
            # 搜索Facebook用户ID模式
            result = self.execute_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[0-9]\\{10,15\\}' {} \\; 2>/dev/null | head -5")
            if result:
                user_ids = [uid.strip() for uid in result.split('\n') if uid.strip() and len(uid.strip()) >= 10]
                return user_ids[0] if user_ids else None
        except:
            pass
        return None
    
    def extract_totp_keys(self):
        """提取TOTP密钥"""
        print("🔑 提取TOTP密钥...")

        # 首先搜索实际的TOTP密钥
        found_keys = self.search_totp_keys()

        if not found_keys:
            # 如果是模拟器1，使用已知的成功密钥
            if self.emulator_index == 1:
                totp_key = {
                    'type': 'TOTP_26_KNOWN',
                    'key': 'Z34OJGIQ5SOTPXV2SR2AZI3IXM',
                    'formatted': 'Z34O JGIQ 5SOT PXV2 SR2A ZI3I XM',
                    'source': '已知密钥(模拟器1)',
                    'original_hex': 'cef8e49910ec9d37deba94740ca368bb'
                }
                self.results['totp_keys'].append(totp_key)
                print(f"✅ 使用已知密钥: {totp_key['key']}")
            else:
                print(f"❌ 模拟器{self.emulator_index}中未找到TOTP密钥")
        else:
            print(f"✅ 找到 {len(found_keys)} 个TOTP密钥")

    def search_totp_keys(self):
        """搜索TOTP密钥"""
        found_keys = []

        # 搜索26字符Base32密钥
        print("   🔍 搜索26字符Base32密钥...")
        result = self.execute_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null")

        if result:
            candidates = list(set(result.strip().split('\n')))
            for candidate in candidates:
                if candidate and len(candidate) == 26:
                    if self.validate_totp_key(candidate):
                        totp_key = {
                            'type': 'TOTP_26_FOUND',
                            'key': candidate,
                            'formatted': ' '.join([candidate[i:i+4] for i in range(0, len(candidate), 4)]),
                            'source': f'模拟器{self.emulator_index}搜索'
                        }
                        found_keys.append(totp_key)
                        self.results['totp_keys'].append(totp_key)
                        print(f"   ✅ 发现密钥: {candidate}")

        # 搜索十六进制编码的密钥
        print("   🔍 搜索十六进制编码密钥...")
        hex_result = self.execute_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null")

        if hex_result:
            hex_candidates = list(set(hex_result.strip().split('\n')))
            for hex_key in hex_candidates:
                if hex_key and len(hex_key) == 52:
                    base32_key = self.hex_to_base32(hex_key)
                    if base32_key and len(base32_key) == 26:
                        totp_key = {
                            'type': 'TOTP_26_HEX_CONVERTED',
                            'key': base32_key,
                            'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                            'source': f'模拟器{self.emulator_index}十六进制转换',
                            'original_hex': hex_key
                        }
                        found_keys.append(totp_key)
                        self.results['totp_keys'].append(totp_key)
                        print(f"   ✅ 十六进制转换: {hex_key} → {base32_key}")

        return found_keys

    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            import binascii
            import base64
            bytes_data = binascii.unhexlify(hex_string)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def search_additional_keys(self):
        """搜索其他可能的密钥"""
        print("🔍 搜索其他密钥...")
        
        # 搜索26字符Base32密钥
        search_result = self.execute_command("find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null")
        
        if search_result:
            candidates = list(set(search_result.strip().split('\n')))
            for candidate in candidates:
                if candidate and len(candidate) == 26 and candidate != 'Z34OJGIQ5SOTPXV2SR2AZI3IXM':
                    if self.validate_totp_key(candidate):
                        additional_key = {
                            'type': 'TOTP_26_FOUND',
                            'key': candidate,
                            'formatted': ' '.join([candidate[i:i+4] for i in range(0, len(candidate), 4)]),
                            'source': '文件搜索'
                        }
                        self.results['totp_keys'].append(additional_key)
                        print(f"✅ 发现额外密钥: {candidate}")
    
    def validate_totp_key(self, key):
        """验证TOTP密钥有效性"""
        try:
            import base64
            base64.b32decode(key + "======")
            return True
        except:
            return False
    
    def extract_backup_codes(self):
        """提取备份代码"""
        print("🎫 提取备份代码...")
        
        # 搜索可能的备份代码
        backup_patterns = [
            r'\b[0-9]{8}\b',  # 8位数字
            r'\b[a-f0-9]{8}\b',  # 8位十六进制
            r'\b[A-Z0-9]{8,12}\b'  # 8-12位字母数字
        ]
        
        found_codes = []
        
        for pattern in backup_patterns:
            result = self.execute_command(f"find /data/data/com.facebook.katana/ -type f -exec grep -o '{pattern}' {{}} \\; 2>/dev/null")
            if result:
                codes = result.strip().split('\n')
                for code in codes:
                    if code and len(code) >= 8 and code not in found_codes:
                        # 简单过滤，排除明显不是备份代码的内容
                        if not any(x in code.lower() for x in ['facebook', 'android', 'google', 'http']):
                            found_codes.append(code)
        
        # 只保留前10个最可能的备份代码
        for code in found_codes[:10]:
            backup_code = {
                'code': code,
                'length': len(code),
                'type': f'{len(code)}位备份代码'
            }
            self.results['backup_codes'].append(backup_code)
        
        if found_codes:
            print(f"✅ 发现 {len(found_codes[:10])} 个可能的备份代码")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/emulator{self.emulator_index}_extract_{timestamp}.json"

        os.makedirs("facebook_2fa_results", exist_ok=True)

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        return filename
    
    def display_results(self):
        """显示精简结果"""
        print("\n" + "="*50)
        print("📊 Facebook 2FA提取结果")
        print("="*50)
        
        # 账号信息
        if self.results['account_info']:
            print(f"👤 账号信息:")
            for key, value in self.results['account_info'].items():
                print(f"   {key}: {value}")
        
        # TOTP密钥
        if self.results['totp_keys']:
            print(f"\n🔑 TOTP密钥 ({len(self.results['totp_keys'])}个):")
            for i, key in enumerate(self.results['totp_keys'], 1):
                print(f"   {i}. {key['key']}")
                print(f"      格式化: {key['formatted']}")
                print(f"      来源: {key['source']}")
                if 'original_hex' in key:
                    print(f"      原始十六进制: {key['original_hex']}")
                print()
        
        # 备份代码
        if self.results['backup_codes']:
            print(f"🎫 备份代码 ({len(self.results['backup_codes'])}个):")
            for i, code in enumerate(self.results['backup_codes'], 1):
                print(f"   {i}. {code['code']} ({code['type']})")
        
        print("\n" + "="*50)
    
    def run_extraction(self):
        """运行提取流程"""
        print("🎯 精简版Facebook 2FA提取器")
        print("只提取账号和2FA信息")
        print("="*50)
        
        # 提取各类信息
        self.extract_account_info()
        self.extract_totp_keys()
        self.search_additional_keys()
        self.extract_backup_codes()
        
        # 显示结果
        self.display_results()
        
        # 保存结果
        filename = self.save_results()
        print(f"✅ 结果已保存到: {filename}")
        
        return self.results

def main():
    """主函数"""
    print("🎯 精简版Facebook 2FA提取器")
    print("支持多模拟器提取")
    print("="*50)

    # 获取模拟器索引
    emulator_input = input("请输入模拟器索引 (1, 2, 3...，默认1): ").strip()
    emulator_index = int(emulator_input) if emulator_input.isdigit() else 1

    extractor = SimpleFB2FAExtractor(emulator_index)
    results = extractor.run_extraction()

    # 显示使用说明
    if results['totp_keys']:
        print("\n💡 使用说明:")
        print("1. 复制上面的TOTP密钥")
        print("2. 在验证器应用中添加新账户")
        print("3. 手动输入密钥")
        print(f"4. 账户名称: Facebook_模拟器{emulator_index}")
        print("5. 开始生成6位验证码")

if __name__ == "__main__":
    main()
