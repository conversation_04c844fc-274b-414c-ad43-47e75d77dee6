#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新Facebook 2FA提取器
重新设计的Facebook双因素认证数据提取工具
支持多种2FA格式和深度扫描
"""

import os
import sys
import json
import re
import base64
import sqlite3
import xml.etree.ElementTree as ET
from datetime import datetime
import hashlib

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from 雷电API import Dnconsole

class NewFacebook2FAExtractor:
    def __init__(self, emulator_index=11):
        """初始化全新Facebook 2FA提取器"""
        try:
            # 雷电模拟器路径配置
            base_paths = [
                r"G:\leidian\LDPlayer9",
                r"G:\LDPlayer\LDPlayer9", 
                r"C:\LDPlayer\LDPlayer9",
                r"D:\LDPlayer\LDPlayer9",
                r"E:\LDPlayer\LDPlayer9"
            ]
            
            self.base_path = None
            for path in base_paths:
                if os.path.exists(os.path.join(path, "ld.exe")):
                    self.base_path = path
                    break
            
            if not self.base_path:
                raise FileNotFoundError("未找到雷电模拟器安装路径")
            
            self.share_path = os.path.expanduser("~/Documents/leidian64")
            self.emulator_index = emulator_index
            
            # 初始化雷电控制台
            self.ld = Dnconsole(
                base_path=self.base_path,
                share_path=self.share_path,
                emulator_id=emulator_index
            )
            
            print(f"🔐 全新Facebook 2FA提取器初始化成功")
            print(f"📱 目标模拟器: {emulator_index}")
            print(f"🔧 雷电路径: {self.base_path}")
            
            # 初始化提取结果
            self.extraction_results = {
                'totp_keys': [],
                'backup_codes': [],
                'auth_tokens': [],
                'user_info': {},
                'raw_data': []
            }
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def execute_command(self, command):
        """执行ADB命令"""
        try:
            success, output = self.ld.execute_ld(self.emulator_index, command)
            return output if success else None
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return None

    def execute_root_command(self, command):
        """执行Root权限命令"""
        try:
            # 由于模拟器已经是Root权限，直接执行命令
            success, output = self.ld.execute_ld(self.emulator_index, command)
            if success and output:
                return output

            return None
        except Exception as e:
            print(f"❌ Root命令执行失败: {e}")
            return None
    
    def check_facebook_installation(self):
        """检查Facebook应用安装状态"""
        print("\n📱 检查Facebook应用状态...")
        print("=" * 50)
        
        # 检查Facebook相关应用
        fb_packages = [
            'com.facebook.katana',      # Facebook主应用
            'com.facebook.orca',        # Messenger
            'com.facebook.lite',        # Facebook Lite
            'com.facebook.mlite'        # Messenger Lite
        ]
        
        installed_apps = []
        
        for package in fb_packages:
            check_result = self.execute_command(f"pm list packages | grep {package}")
            if check_result and package in check_result:
                installed_apps.append(package)
                print(f"✅ 发现: {package}")
                
                # 获取应用版本信息
                version_info = self.execute_command(f"dumpsys package {package} | grep versionName")
                if version_info:
                    version = version_info.strip().split('=')[-1] if '=' in version_info else 'Unknown'
                    print(f"   版本: {version}")
        
        if not installed_apps:
            print("❌ 未发现任何Facebook应用")
            return False
        
        print(f"\n✅ 总共发现 {len(installed_apps)} 个Facebook应用")
        return installed_apps
    
    def extract_facebook_user_info(self):
        """提取Facebook用户信息"""
        print("\n👤 提取Facebook用户信息...")
        print("=" * 50)
        
        user_info = {}
        
        # Facebook配置文件路径
        config_paths = [
            "/data/data/com.facebook.katana/shared_prefs/",
            "/data/data/com.facebook.katana/databases/",
            "/data/data/com.facebook.katana/files/"
        ]
        
        for config_path in config_paths:
            print(f"\n📁 搜索路径: {config_path}")
            
            # 列出文件 - 直接使用命令
            files_result = self.execute_command(f"find {config_path} -type f 2>/dev/null")
            if not files_result:
                print(f"   ❌ 无法访问路径: {config_path}")
                continue

            files = [f.strip() for f in files_result.split('\n') if f.strip()]
            print(f"   📊 发现 {len(files)} 个文件")

            for file_path in files[:20]:  # 增加文件数量
                try:
                    file_name = os.path.basename(file_path)
                    print(f"   📄 检查: {file_name}")

                    # 读取文件内容
                    content = self.execute_command(f"cat '{file_path}' 2>/dev/null")
                    if content and len(content) > 10:
                        # 提取用户信息
                        extracted_info = self.parse_user_info(content, file_name)
                        user_info.update(extracted_info)
                
                except Exception as e:
                    continue
        
        self.extraction_results['user_info'] = user_info
        
        if user_info:
            print(f"\n✅ 提取到用户信息:")
            for key, value in user_info.items():
                print(f"   {key}: {value}")
        
        return user_info
    
    def parse_user_info(self, content, source_file):
        """解析用户信息"""
        user_info = {}
        
        # 用户信息模式
        patterns = [
            (r'"user_id"[:\s]*"?(\d+)"?', 'user_id'),
            (r'"uid"[:\s]*"?(\d+)"?', 'user_id'),
            (r'"email"[:\s]*"([^"]+@[^"]+)"', 'email'),
            (r'"username"[:\s]*"([^"]+)"', 'username'),
            (r'"name"[:\s]*"([^"]+)"', 'name'),
            (r'"phone"[:\s]*"(\+?\d{10,15})"', 'phone'),
        ]
        
        for pattern, info_type in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                user_info[info_type] = matches[0]
                print(f"      🔍 发现 {info_type}: {matches[0]}")
        
        return user_info
    
    def extract_totp_keys(self):
        """提取TOTP密钥"""
        print("\n🔑 提取TOTP密钥...")
        print("=" * 50)
        
        found_keys = []
        
        # 搜索所有可能包含TOTP密钥的文件
        search_paths = [
            "/data/data/com.facebook.katana/",
            "/data/data/com.google.android.apps.authenticator2/",
            "/data/data/com.authy.authy/",
            "/data/data/org.fedorahosted.freeotp/"
        ]
        
        for search_path in search_paths:
            print(f"\n📁 搜索路径: {search_path}")
            
            # 递归搜索所有文件
            all_files = self.execute_command(f"find {search_path} -type f 2>/dev/null")
            if not all_files:
                print(f"   ❌ 路径不存在或无权限: {search_path}")
                continue

            files = [f.strip() for f in all_files.split('\n') if f.strip()]
            print(f"   📊 发现 {len(files)} 个文件")

            for file_path in files[:50]:  # 增加检查的文件数量
                try:
                    file_name = os.path.basename(file_path)

                    # 跳过二进制文件
                    if any(ext in file_name.lower() for ext in ['.so', '.dex', '.apk', '.png', '.jpg', '.gif']):
                        continue

                    print(f"      📄 读取: {file_name}")

                    # 读取文件内容
                    content = self.execute_command(f"cat '{file_path}' 2>/dev/null")
                    if content and len(content) > 10:
                        # 搜索TOTP密钥
                        keys = self.search_totp_patterns(content, file_name)
                        found_keys.extend(keys)
                
                except Exception as e:
                    continue
        
        self.extraction_results['totp_keys'] = found_keys
        
        if found_keys:
            print(f"\n🎉 找到 {len(found_keys)} 个TOTP密钥:")
            for i, key_info in enumerate(found_keys, 1):
                print(f"\n🔑 密钥 #{i}:")
                print(f"   密钥: {key_info['key']}")
                print(f"   长度: {key_info['length']} 字符")
                print(f"   类型: {key_info['type']}")
                print(f"   来源: {key_info['source']}")
                if key_info.get('formatted'):
                    print(f"   格式化: {key_info['formatted']}")
        else:
            print("\n❌ 未找到TOTP密钥")
        
        return found_keys
    
    def search_totp_patterns(self, content, source_file):
        """搜索TOTP模式"""
        found_keys = []
        
        # 标准Base32 TOTP密钥模式
        totp_patterns = [
            # 最常见的标准长度
            (r'\b[A-Z2-7]{26}\b', 'TOTP_26_STANDARD', '26字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{32}\b', 'TOTP_32_STANDARD', '32字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{20}\b', 'TOTP_20_STANDARD', '20字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{24}\b', 'TOTP_24_STANDARD', '24字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{16}\b', 'TOTP_16_STANDARD', '16字符标准TOTP密钥'),
            
            # TOTP URI格式
            (r'otpauth://totp/[^?\s]+\?secret=([A-Z2-7]+)', 'TOTP_URI_SECRET', 'URI中的TOTP密钥'),
            (r'otpauth://totp/[^\s]+', 'TOTP_URI_FULL', '完整TOTP URI'),
        ]
        
        for pattern, key_type, description in totp_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # 验证是否为有效的Base32
                if self.is_valid_base32_totp(match):
                    key_info = {
                        'key': match,
                        'length': len(match),
                        'type': key_type,
                        'description': description,
                        'source': source_file,
                        'formatted': self.format_totp_key(match),
                        'validation': self.validate_totp_key(match)
                    }
                    found_keys.append(key_info)
                    
                    print(f"      🔑 发现TOTP密钥: {match}")
                    print(f"         类型: {description}")
                    print(f"         长度: {len(match)} 字符")
        
        return found_keys
    
    def is_valid_base32_totp(self, key):
        """验证是否为有效的Base32 TOTP密钥"""
        try:
            # 检查字符集
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # 检查长度
            if len(key) < 16 or len(key) > 64:
                return False
            
            # 检查是否为有意义的英文单词
            common_words = ['FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'WHATSAPP']
            if any(word in key.upper() for word in common_words):
                return False
            
            # 尝试Base32解码
            padded_key = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded_key)
            
            # 检查解码后的长度是否合理（通常10-20字节）
            if len(decoded) < 10 or len(decoded) > 64:
                return False
            
            return True
            
        except Exception:
            return False
    
    def format_totp_key(self, key):
        """格式化TOTP密钥"""
        # 每4个字符一组
        return ' '.join([key[i:i+4] for i in range(0, len(key), 4)])
    
    def validate_totp_key(self, key):
        """验证TOTP密钥"""
        validation = {
            'is_valid': False,
            'decoded_length': 0,
            'entropy': 0
        }
        
        try:
            # Base32解码
            padded_key = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded_key)
            
            validation['is_valid'] = True
            validation['decoded_length'] = len(decoded)
            
            # 计算熵值（随机性）
            byte_counts = {}
            for byte in decoded:
                byte_counts[byte] = byte_counts.get(byte, 0) + 1
            
            entropy = 0
            for count in byte_counts.values():
                p = count / len(decoded)
                entropy -= p * (p.bit_length() - 1) if p > 0 else 0
            
            validation['entropy'] = entropy
            
        except Exception as e:
            validation['error'] = str(e)
        
        return validation
    
    def extract_backup_codes(self):
        """提取备份代码"""
        print("\n🎫 提取备份代码...")
        print("=" * 50)
        
        found_codes = []
        
        # 搜索Facebook应用数据
        search_result = self.execute_command("find /data/data/com.facebook.katana/ -type f 2>/dev/null")
        if not search_result:
            print("❌ 无法访问Facebook应用数据")
            return found_codes

        files = [f.strip() for f in search_result.split('\n') if f.strip()]
        print(f"📊 搜索 {len(files)} 个文件中的备份代码...")

        for file_path in files[:100]:  # 增加搜索文件数量
            try:
                file_name = os.path.basename(file_path)
                print(f"   📄 检查: {file_name}")

                content = self.execute_command(f"cat '{file_path}' 2>/dev/null")
                if content:
                    codes = self.search_backup_codes(content, os.path.basename(file_path))
                    found_codes.extend(codes)
            except:
                continue
        
        self.extraction_results['backup_codes'] = found_codes
        
        if found_codes:
            print(f"\n🎉 找到 {len(found_codes)} 个备份代码:")
            for code in found_codes[:10]:  # 显示前10个
                print(f"   🎫 {code['code']} (来源: {code['source']})")
        
        return found_codes
    
    def search_backup_codes(self, content, source_file):
        """搜索备份代码"""
        found_codes = []
        
        # 备份代码模式
        patterns = [
            (r'\b[A-Z0-9]{8}\b', 'BACKUP_8'),
            (r'\b[A-Z0-9]{10}\b', 'BACKUP_10'),
            (r'\b[A-Z0-9]{12}\b', 'BACKUP_12'),
            (r'\b\d{8}\b', 'NUMERIC_8'),
            (r'\b\d{10}\b', 'NUMERIC_10'),
        ]
        
        for pattern, code_type in patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                # 过滤明显不是备份代码的内容
                if self.is_likely_backup_code(match):
                    found_codes.append({
                        'code': match,
                        'type': code_type,
                        'source': source_file,
                        'length': len(match)
                    })
        
        return found_codes
    
    def is_likely_backup_code(self, code):
        """判断是否可能是备份代码"""
        # 过滤明显不是备份代码的内容
        exclude_patterns = [
            r'^\d{4}0{4,}$',  # 类似10000000的模式
            r'^0+$',          # 全是0
            r'^1+$',          # 全是1
            r'^\d{13,}$',     # 太长的数字（可能是时间戳）
        ]
        
        for pattern in exclude_patterns:
            if re.match(pattern, code):
                return False
        
        return True
    
    def save_extraction_results(self):
        """保存提取结果"""
        try:
            # 创建结果目录
            os.makedirs('facebook_2fa_results', exist_ok=True)
            
            # 添加提取摘要
            self.extraction_results['extraction_summary'] = {
                'extraction_time': datetime.now().isoformat(),
                'emulator_index': self.emulator_index,
                'total_totp_keys': len(self.extraction_results['totp_keys']),
                'total_backup_codes': len(self.extraction_results['backup_codes']),
                'user_info_found': bool(self.extraction_results['user_info'])
            }
            
            # 保存到文件
            filename = f"facebook_2fa_results/new_fb_2fa_extract_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.extraction_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 提取结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    
    def run_extraction(self):
        """运行完整的提取流程"""
        print("🔐 全新Facebook 2FA提取器")
        print("重新设计的Facebook双因素认证数据提取工具")
        print("=" * 60)
        
        try:
            # 1. 检查Facebook应用
            installed_apps = self.check_facebook_installation()
            if not installed_apps:
                return False
            
            # 2. 提取用户信息
            user_info = self.extract_facebook_user_info()
            
            # 3. 提取TOTP密钥
            totp_keys = self.extract_totp_keys()
            
            # 4. 提取备份代码
            backup_codes = self.extract_backup_codes()
            
            # 5. 显示提取摘要
            print(f"\n📊 提取摘要:")
            print("=" * 50)
            print(f"用户信息: {len(user_info)} 项")
            print(f"TOTP密钥: {len(totp_keys)} 个")
            print(f"备份代码: {len(backup_codes)} 个")
            
            # 6. 保存结果
            result_file = self.save_extraction_results()
            
            if result_file:
                print(f"\n🎉 Facebook 2FA提取完成!")
                return True
            else:
                print(f"\n❌ 保存结果失败")
                return False
                
        except Exception as e:
            print(f"❌ 提取流程失败: {e}")
            return False

def main():
    """主函数"""
    print("🔐 全新Facebook 2FA提取器")
    print("=" * 40)
    
    try:
        # 创建提取器实例
        extractor = NewFacebook2FAExtractor(emulator_index=11)
        
        # 运行提取流程
        success = extractor.run_extraction()
        
        if success:
            print("\n🎉 Facebook 2FA提取成功！")
            print("📁 请查看facebook_2fa_results目录中的结果文件")
        else:
            print("\n❌ Facebook 2FA提取失败")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
