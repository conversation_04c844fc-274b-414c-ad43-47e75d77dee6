#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试元素查找算法
"""

import uiautomator2 as u2
import xml.etree.ElementTree as ET
import re

def debug_element_algorithm(x, y):
    """调试元素查找算法"""
    print(f"🔍 调试坐标 ({x}, {y}) 的元素查找算法")
    
    try:
        # 连接设备
        d = u2.connect('127.0.0.1:5557')
        
        # 获取UI层次结构
        xml_content = d.dump_hierarchy()
        root = ET.fromstring(xml_content)
        
        print(f"📄 XML根元素: {root.tag}")
        print(f"📄 XML内容长度: {len(xml_content)}")
        
        # 使用Web控制器中的算法
        def find_element_at_point_web(element, target_x, target_y, depth=0):
            print(f"  {'  ' * depth}检查元素: {element.get('class', 'Unknown')} (深度 {depth})")
            
            bounds = element.get('bounds', '')
            if bounds:
                print(f"  {'  ' * depth}边界: {bounds}")
                # 解析bounds格式: [x1,y1][x2,y2]
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    print(f"  {'  ' * depth}坐标范围: ({x1},{y1}) - ({x2},{y2})")
                    
                    if x1 <= target_x <= x2 and y1 <= target_y <= y2:
                        print(f"  {'  ' * depth}✅ 坐标匹配!")
                        
                        # 收集所有匹配的元素
                        matching_elements = []
                        
                        # 检查子元素
                        for child in element:
                            child_result = find_element_at_point_web(child, target_x, target_y, depth + 1)
                            if child_result:
                                matching_elements.append((depth + 1, child_result))
                        
                        # 如果有子元素匹配，返回最深层的
                        if matching_elements:
                            deepest_depth = max(matching_elements, key=lambda x: x[0])[0]
                            deepest_elements = [elem for d, elem in matching_elements if d == deepest_depth]
                            print(f"  {'  ' * depth}返回最深层子元素 (深度 {deepest_depth})")
                            return deepest_elements[0]  # 返回第一个最深层元素
                        
                        # 否则返回当前元素信息
                        print(f"  {'  ' * depth}返回当前元素")
                        return {
                            'bounds': bounds,
                            'class': element.get('class', ''),
                            'text': element.get('text', ''),
                            'resource-id': element.get('resource-id', ''),
                            'content-desc': element.get('content-desc', ''),
                            'clickable': element.get('clickable', 'false'),
                            'enabled': element.get('enabled', 'false'),
                            'package': element.get('package', ''),
                            'depth': depth
                        }
                    else:
                        print(f"  {'  ' * depth}❌ 坐标不匹配")
                else:
                    print(f"  {'  ' * depth}❌ 边界格式错误")
            else:
                print(f"  {'  ' * depth}❌ 无边界信息")
            
            return None
        
        print(f"\n🔍 使用Web控制器算法:")
        result_web = find_element_at_point_web(root, x, y)
        
        if result_web:
            print(f"\n✅ Web算法找到元素:")
            for key, value in result_web.items():
                if value:
                    print(f"   {key}: {value}")
        else:
            print(f"\n❌ Web算法未找到元素")
        
        # 使用简单算法对比
        def find_element_simple(element, target_x, target_y, depth=0):
            bounds = element.get('bounds', '')
            if bounds:
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    if x1 <= target_x <= x2 and y1 <= target_y <= y2:
                        # 检查子元素
                        best_child = None
                        max_depth = depth
                        
                        for child in element:
                            child_result = find_element_simple(child, target_x, target_y, depth + 1)
                            if child_result and child_result[1] > max_depth:
                                best_child = child_result[0]
                                max_depth = child_result[1]
                        
                        if best_child:
                            return (best_child, max_depth)
                        
                        # 返回当前元素
                        return ({
                            'bounds': bounds,
                            'class': element.get('class', ''),
                            'text': element.get('text', ''),
                            'resource-id': element.get('resource-id', ''),
                            'content-desc': element.get('content-desc', ''),
                            'clickable': element.get('clickable', 'false'),
                            'enabled': element.get('enabled', 'false'),
                            'package': element.get('package', ''),
                            'depth': depth
                        }, depth)
            return None
        
        print(f"\n🔍 使用简单算法:")
        result_simple = find_element_simple(root, x, y)
        
        if result_simple:
            element_info, depth = result_simple
            print(f"\n✅ 简单算法找到元素 (深度 {depth}):")
            for key, value in element_info.items():
                if value and key != 'depth':
                    print(f"   {key}: {value}")
        else:
            print(f"\n❌ 简单算法未找到元素")
        
        return result_web, result_simple
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    # 测试已知有元素的坐标
    debug_element_algorithm(100, 84)  # v2rayNG应用图标
