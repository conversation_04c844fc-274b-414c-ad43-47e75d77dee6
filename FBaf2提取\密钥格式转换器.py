#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Facebook TOTP密钥格式转换器
将提取的十六进制密钥转换为标准Base32格式
"""

import base64
import binascii
import re

def hex_to_base32(hex_string):
    """将十六进制转换为Base32格式"""
    try:
        # 清理十六进制字符串
        hex_clean = hex_string.replace(' ', '').lower()
        
        # 验证是否为有效十六进制
        if not re.match(r'^[0-9a-f]+$', hex_clean):
            return None
        
        # 转换为字节
        bytes_data = binascii.unhexlify(hex_clean)
        
        # 转换为Base32
        base32_string = base64.b32encode(bytes_data).decode('ascii')
        
        # 移除填充字符
        base32_clean = base32_string.rstrip('=')
        
        # 格式化为标准TOTP格式 (每4个字符一组)
        formatted = ' '.join([base32_clean[i:i+4] for i in range(0, len(base32_clean), 4)])
        
        return {
            'clean': base32_clean,
            'formatted': formatted,
            'length': len(base32_clean)
        }
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

def main():
    """主函数"""
    print("🔑 Facebook TOTP密钥格式转换器")
    print("=" * 50)
    
    # 从您的文件中提取的十六进制密钥
    hex_key = "cef8e49910ec9d37deba94740ca368bb"
    
    print(f"📝 原始十六进制密钥: {hex_key}")
    print(f"📏 长度: {len(hex_key)} 字符")
    
    # 转换为Base32
    result = hex_to_base32(hex_key)
    
    if result:
        print(f"\n✅ 转换成功!")
        print(f"🔑 Base32密钥 (清洁版): {result['clean']}")
        print(f"🔑 Base32密钥 (格式化): {result['formatted']}")
        print(f"📏 Base32长度: {result['length']} 字符")
        
        print(f"\n📱 在验证器应用中使用:")
        print(f"=" * 30)
        print(f"账户名称: Facebook")
        print(f"密钥: {result['clean']}")
        print(f"或格式化输入: {result['formatted']}")
        print(f"时间间隔: 30秒")
        
        print(f"\n🎯 推荐的验证器应用:")
        print(f"1. Google Authenticator")
        print(f"2. Microsoft Authenticator") 
        print(f"3. Authy")
        
        print(f"\n🌐 使用步骤:")
        print(f"1. 在验证器应用中点击 '+' 添加账户")
        print(f"2. 选择 '手动输入' 或 '输入密钥'")
        print(f"3. 输入上面的Base32密钥")
        print(f"4. 保存后开始生成6位验证码")
        print(f"5. 在Facebook登录时使用这些验证码")
        
    else:
        print(f"❌ 转换失败")
        print(f"💡 建议使用备份代码: facebook")

if __name__ == "__main__":
    main()
