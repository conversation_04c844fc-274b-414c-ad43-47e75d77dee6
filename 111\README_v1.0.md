# Android UI自动化工具 v1.0

基于UIAutomator2的Android设备UI自动化控制工具，提供现代化Web界面操作。

## 🚀 项目特色

- **🌐 Web界面控制** - 无需命令行，通过浏览器操作
- **📱 多设备支持** - 自动检测并管理多个Android设备
- **🔍 UI元素检测** - 智能识别屏幕元素并提取详细信息
- **⚡ 异步执行** - 高性能异步操作架构
- **🎯 精确控制** - 支持坐标点击、滑动、按键等操作

## 📋 环境要求

- Python 3.7+
- Android设备或模拟器
- ADB工具
- UIAutomator2

## 🛠️ 安装依赖

```bash
pip install uiautomator2 flask pillow
```

## 📱 设备准备

1. 启动Android设备或模拟器
2. 确保ADB连接正常：
   ```bash
   adb devices
   ```
3. 安装UIAutomator2服务：
   ```bash
   python -m uiautomator2 init
   ```

## 🎯 功能特性

### 核心功能
1. **实时屏幕显示** - 查看设备当前屏幕状态
2. **双模式操作** - 点击模式和检查模式切换
3. **UI元素检查** - 获取元素的详细属性信息
4. **坐标显示** - 鼠标悬停显示精确坐标
5. **多设备管理** - 支持同时连接多个设备

### 操作功能
6. **快捷按键** - Home、Back、Menu、Recent按键
7. **滑动操作** - 上下左右滑动功能
8. **文本输入** - 向设备发送文本
9. **截图保存** - 保存当前屏幕截图
10. **设备信息** - 查看设备详细信息

## 🚀 快速开始

1. **启动程序**：
   ```bash
   python web_controller.py
   ```

2. **打开浏览器**：
   访问 http://localhost:8080

3. **开始使用**：
   - **点击模式**：直接点击屏幕进行操作
   - **检查模式**：点击元素查看详细信息
   - 使用左侧控制面板进行快捷操作

## 🔍 UI元素检查功能

在检查模式下，点击任意UI元素可以获取：

- **XPath路径** - 元素的完整路径
- **class** - 元素类名 (如 android.widget.EditText)
- **resource-id** - 资源ID (如 com.instagram.android:id/row_thread_composer_edittext)
- **text** - 元素文本内容
- **bounds** - 元素边界坐标
- **activity** - 当前Activity
- **package** - 应用包名
- **各种属性** - clickable, enabled, focusable等

## 💻 编程接口示例

```python
import uiautomator2 as u2

# 连接设备
d = u2.connect('127.0.0.1:5557')

# 通过资源ID点击
d(resourceId='com.instagram.android:id/row_thread_composer_edittext').click()

# 通过文本点击
d(text='发消息...').click()

# 通过类名点击
d(className='android.widget.EditText').click()

# 组合条件
d(text='发消息...', className='android.widget.EditText').click()

# 坐标点击
d.click(100, 200)

# 滑动操作
d.swipe_ext("up")    # 向上滑动
d.swipe_ext("down")  # 向下滑动
d.swipe_ext("left")  # 向左滑动
d.swipe_ext("right") # 向右滑动
```

## 📁 项目结构

```
ui_automation_tool/
├── web_controller.py           # 主控制器 (启动这个文件)
├── debug_element_inspection.py # 调试工具
├── test_element_inspection.py  # 测试脚本
├── get_current_ui.py          # UI状态获取
├── simple_element_test.py     # 简单测试
├── test_routes.py             # API测试
├── README.md                  # 项目说明
└── .gitignore                 # Git忽略文件
```

## 🌐 Web API接口

- `GET /` - 主界面
- `GET /screenshot` - 获取屏幕截图
- `GET /device_info` - 获取设备信息
- `POST /click` - 执行点击操作
- `POST /inspect` - 检查UI元素
- `POST /swipe` - 执行滑动操作
- `POST /key` - 发送按键
- `POST /text` - 发送文本

## 🔧 技术栈

- **后端**: Python Flask
- **前端**: HTML5 + CSS3 + JavaScript
- **自动化**: UIAutomator2
- **设备通信**: ADB

## 📝 版本历史

### v1.0 (2025-07-06)
- ✅ 基础UI自动化功能
- ✅ Web界面控制
- ✅ 多设备支持
- ✅ UI元素检测
- ✅ 参数提取功能
- ✅ 异步执行架构

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

---

**开发者**: AI Assistant  
**创建时间**: 2025年7月6日  
**版本**: 1.0
