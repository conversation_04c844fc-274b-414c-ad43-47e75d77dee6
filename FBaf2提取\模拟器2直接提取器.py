#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器2直接提取器
直接从模拟器2提取Facebook 2FA信息
"""

import os
import json
import subprocess
import re
from datetime import datetime

class Emulator2DirectExtractor:
    def __init__(self):
        self.device_id = "emulator-5556"
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.results = {
            'emulator': 'emulator-2',
            'account_info': {},
            'totp_keys': [],
            'backup_codes': [],
            'files_checked': []
        }
        
    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            full_command = f'"{self.adb_path}" -s {self.device_id} shell "{command}"'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            return None
    
    def get_facebook_files(self):
        """获取Facebook文件列表"""
        print("📁 获取Facebook文件列表...")
        
        # 获取主要目录下的文件
        directories = [
            "/data/data/com.facebook.katana/shared_prefs/",
            "/data/data/com.facebook.katana/databases/",
            "/data/data/com.facebook.katana/files/",
            "/data/data/com.facebook.katana/cache/"
        ]
        
        all_files = []
        
        for directory in directories:
            print(f"   🔍 检查目录: {directory}")
            files_result = self.execute_adb_command(f"su -c 'ls -la {directory} 2>/dev/null'")
            
            if files_result:
                print(f"      ✅ 目录存在，包含文件:")
                for line in files_result.split('\n'):
                    if line.strip() and not line.startswith('total') and not line.startswith('d'):
                        file_name = line.split()[-1] if line.split() else ""
                        if file_name and file_name not in ['.', '..']:
                            full_path = f"{directory}{file_name}"
                            all_files.append(full_path)
                            print(f"         📄 {file_name}")
            else:
                print(f"      ❌ 目录不存在或无法访问")
        
        self.results['files_checked'] = all_files
        return all_files
    
    def read_file_content(self, file_path):
        """读取文件内容"""
        print(f"   📖 读取文件: {os.path.basename(file_path)}")
        
        # 尝试直接读取
        content = self.execute_adb_command(f"su -c 'cat {file_path} 2>/dev/null'")
        
        if not content:
            # 如果是二进制文件，使用strings
            content = self.execute_adb_command(f"su -c 'strings {file_path} 2>/dev/null'")
        
        return content if content else ""
    
    def search_totp_in_content(self, content, source_file):
        """在内容中搜索TOTP密钥"""
        if not content:
            return []
        
        found_keys = []
        
        # 搜索26字符Base32密钥
        base32_pattern = r'[A-Z2-7]{26}'
        matches = re.findall(base32_pattern, content)
        
        for match in matches:
            if self.validate_totp_key(match):
                key_info = {
                    'key': match,
                    'formatted': ' '.join([match[i:i+4] for i in range(0, len(match), 4)]),
                    'source': os.path.basename(source_file),
                    'type': 'TOTP_26_BASE32'
                }
                found_keys.append(key_info)
                print(f"      🔑 发现TOTP密钥: {match}")
        
        # 搜索52字符十六进制密钥
        hex_pattern = r'[a-fA-F0-9]{52}'
        hex_matches = re.findall(hex_pattern, content)
        
        for hex_match in hex_matches:
            base32_key = self.hex_to_base32(hex_match)
            if base32_key and len(base32_key) == 26:
                key_info = {
                    'key': base32_key,
                    'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                    'source': os.path.basename(source_file),
                    'type': 'TOTP_26_HEX_CONVERTED',
                    'original_hex': hex_match
                }
                found_keys.append(key_info)
                print(f"      🔑 十六进制转换: {hex_match} → {base32_key}")
        
        return found_keys
    
    def search_backup_codes_in_content(self, content, source_file):
        """在内容中搜索备份代码"""
        if not content:
            return []
        
        found_codes = []
        
        # 搜索8位数字
        digit_pattern = r'\b[0-9]{8}\b'
        digit_matches = re.findall(digit_pattern, content)
        
        for match in digit_matches:
            # 过滤明显不是备份代码的数字
            if not any(x in match for x in ['20240', '20250', '19700']):  # 过滤日期
                code_info = {
                    'code': match,
                    'type': '8位数字备份代码',
                    'source': os.path.basename(source_file)
                }
                found_codes.append(code_info)
        
        # 搜索8位十六进制
        hex_pattern = r'\b[a-f0-9]{8}\b'
        hex_matches = re.findall(hex_pattern, content)
        
        for match in hex_matches:
            code_info = {
                'code': match,
                'type': '8位十六进制备份代码',
                'source': os.path.basename(source_file)
            }
            found_codes.append(code_info)
        
        return found_codes
    
    def validate_totp_key(self, key):
        """验证TOTP密钥"""
        try:
            import base64
            base64.b32decode(key + "======")
            return True
        except:
            return False
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            import binascii
            import base64
            bytes_data = binascii.unhexlify(hex_string)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def extract_account_info(self):
        """提取账号信息"""
        print("👤 提取账号信息...")
        
        # 搜索用户ID
        user_id_result = self.execute_adb_command("su -c 'find /data/data/com.facebook.katana/ -type f -exec grep -o \"[0-9]\\{10,15\\}\" {} \\; 2>/dev/null | head -5'")
        
        user_id = "未知"
        if user_id_result:
            user_ids = [uid.strip() for uid in user_id_result.split('\n') if uid.strip() and len(uid.strip()) >= 10]
            if user_ids:
                user_id = user_ids[0]
                print(f"   ✅ 发现用户ID: {user_id}")
        
        self.results['account_info'] = {
            'emulator': 'emulator-2',
            'user_id': user_id,
            'username': f'Facebook用户_模拟器2',
            'status': '已搜索'
        }
    
    def run_extraction(self):
        """运行提取流程"""
        print("🎯 模拟器2直接提取器")
        print("="*50)
        
        # 1. 提取账号信息
        self.extract_account_info()
        
        # 2. 获取文件列表
        files = self.get_facebook_files()
        
        if not files:
            print("❌ 未找到Facebook文件")
            return False
        
        print(f"\n🔍 开始分析 {len(files)} 个文件...")
        
        # 3. 逐个分析文件
        for file_path in files:
            content = self.read_file_content(file_path)
            
            if content:
                # 搜索TOTP密钥
                totp_keys = self.search_totp_in_content(content, file_path)
                self.results['totp_keys'].extend(totp_keys)
                
                # 搜索备份代码
                backup_codes = self.search_backup_codes_in_content(content, file_path)
                self.results['backup_codes'].extend(backup_codes)
        
        # 4. 去重
        self.deduplicate_results()
        
        # 5. 显示结果
        self.display_results()
        
        # 6. 保存结果
        filename = self.save_results()
        print(f"\n✅ 提取结果已保存到: {filename}")
        
        return True
    
    def deduplicate_results(self):
        """去重结果"""
        # TOTP密钥去重
        seen_keys = set()
        unique_totp = []
        for key in self.results['totp_keys']:
            if key['key'] not in seen_keys:
                seen_keys.add(key['key'])
                unique_totp.append(key)
        self.results['totp_keys'] = unique_totp
        
        # 备份代码去重
        seen_codes = set()
        unique_codes = []
        for code in self.results['backup_codes']:
            if code['code'] not in seen_codes:
                seen_codes.add(code['code'])
                unique_codes.append(code)
                if len(unique_codes) >= 20:  # 限制20个
                    break
        self.results['backup_codes'] = unique_codes
    
    def display_results(self):
        """显示结果"""
        print("\n" + "="*60)
        print("📊 模拟器2提取结果")
        print("="*60)
        
        # 账号信息
        print("👤 账号信息:")
        for key, value in self.results['account_info'].items():
            print(f"   {key}: {value}")
        
        # TOTP密钥
        if self.results['totp_keys']:
            print(f"\n🔑 TOTP密钥 ({len(self.results['totp_keys'])}个):")
            for i, key in enumerate(self.results['totp_keys'], 1):
                print(f"   {i}. {key['key']}")
                print(f"      格式化: {key['formatted']}")
                print(f"      来源: {key['source']}")
                if 'original_hex' in key:
                    print(f"      原始十六进制: {key['original_hex']}")
                print()
        else:
            print("\n❌ 未发现TOTP密钥")
        
        # 备份代码
        if self.results['backup_codes']:
            print(f"🎫 备份代码 ({len(self.results['backup_codes'])}个):")
            for i, code in enumerate(self.results['backup_codes'][:10], 1):
                print(f"   {i}. {code['code']} ({code['type']}) - 来源: {code['source']}")
        else:
            print("\n❌ 未发现备份代码")
        
        print(f"\n📁 已检查文件: {len(self.results['files_checked'])}个")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/emulator2_direct_{timestamp}.json"
        
        os.makedirs("facebook_2fa_results", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        return filename

def main():
    """主函数"""
    extractor = Emulator2DirectExtractor()
    success = extractor.run_extraction()
    
    if success and extractor.results['totp_keys']:
        print("\n💡 使用说明:")
        print("1. 复制上面的TOTP密钥")
        print("2. 在验证器应用中添加新账户")
        print("3. 手动输入密钥")
        print("4. 账户名称: Facebook_模拟器2")
        print("5. 开始生成6位验证码")
    elif not success:
        print("\n💡 建议:")
        print("1. 确保模拟器2正在运行")
        print("2. 确保Facebook应用已安装并登录")
        print("3. 确保已启用2FA")

if __name__ == "__main__":
    main()
