#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时TOTP验证码工具
实时生成和显示Facebook 2FA验证码
"""

import base64
import hmac
import hashlib
import struct
import time
import os
import json
from datetime import datetime

class RealTimeTOTPTool:
    def __init__(self):
        """初始化实时TOTP工具"""
        self.facebook_secret = "Z34OJGIQ5SOTPXV2SR2AZI3IXM"
        self.time_step = 30
        self.digits = 6
        self.account_info = self.load_account_info()
        
    def load_account_info(self):
        """加载账户信息"""
        try:
            with open('facebook_2fa_results/complete_extract_11_20250723_001827.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data.get('account_info', {})
        except:
            return {
                'user_id': '**************',
                'phone': '*************'
            }
    
    def base32_decode(self, secret):
        """解码Base32密钥"""
        try:
            secret = secret.replace(' ', '').upper()
            while len(secret) % 8 != 0:
                secret += '='
            return base64.b32decode(secret)
        except Exception as e:
            print(f"❌ Base32解码失败: {e}")
            return None
    
    def generate_totp_detailed(self, timestamp=None):
        """生成详细的TOTP信息"""
        try:
            if timestamp is None:
                timestamp = int(time.time())
            
            # 计算时间步数
            time_counter = timestamp // self.time_step
            
            # 解码密钥
            key = self.base32_decode(self.facebook_secret)
            if key is None:
                return None
            
            # 生成HMAC
            time_bytes = struct.pack('>Q', time_counter)
            hmac_hash = hmac.new(key, time_bytes, hashlib.sha1).digest()
            
            # 动态截取
            offset = hmac_hash[-1] & 0x0f
            truncated = struct.unpack('>I', hmac_hash[offset:offset+4])[0]
            truncated &= 0x7fffffff
            
            # 生成验证码
            totp_code = truncated % (10 ** self.digits)
            
            # 计算剩余时间
            remaining_time = self.time_step - (timestamp % self.time_step)
            
            # 计算下一个验证码
            next_timestamp = timestamp + remaining_time
            next_time_counter = next_timestamp // self.time_step
            next_time_bytes = struct.pack('>Q', next_time_counter)
            next_hmac_hash = hmac.new(key, next_time_bytes, hashlib.sha1).digest()
            next_offset = next_hmac_hash[-1] & 0x0f
            next_truncated = struct.unpack('>I', next_hmac_hash[next_offset:next_offset+4])[0]
            next_truncated &= 0x7fffffff
            next_totp_code = next_truncated % (10 ** self.digits)
            
            return {
                'current_code': f"{totp_code:0{self.digits}d}",
                'next_code': f"{next_totp_code:0{self.digits}d}",
                'remaining_time': remaining_time,
                'timestamp': timestamp,
                'time_counter': time_counter,
                'current_time': datetime.fromtimestamp(timestamp).strftime('%H:%M:%S')
            }
            
        except Exception as e:
            print(f"❌ 生成TOTP失败: {e}")
            return None
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def display_header(self):
        """显示头部信息"""
        print("🔑 Facebook 实时TOTP验证码工具")
        print("=" * 60)
        print(f"📱 手机号: {self.account_info.get('phone', '*************')}")
        print(f"🆔 用户ID: {self.account_info.get('user_id', '**************')}")
        print(f"🔐 密钥: {self.facebook_secret}")
        print("=" * 60)
    
    def display_totp_info(self, totp_info):
        """显示TOTP信息"""
        if not totp_info:
            print("❌ 无法生成验证码")
            return
        
        # 进度条
        progress = 30 - totp_info['remaining_time']
        progress_bar = "█" * progress + "░" * (30 - progress)
        
        print(f"🕐 当前时间: {totp_info['current_time']}")
        print(f"🔑 当前验证码: {totp_info['current_code']} (剩余 {totp_info['remaining_time']:2d} 秒)")
        print(f"⏭️  下个验证码: {totp_info['next_code']}")
        print(f"📊 进度: [{progress_bar}] {progress}/30")
        print()
        print(f"🌐 Facebook登录步骤:")
        print(f"1. 访问: https://www.facebook.com")
        print(f"2. 手机号: {self.account_info.get('phone', '*************')}")
        print(f"3. 密码: (需要重置)")
        print(f"4. 验证码: {totp_info['current_code']}")
        print()
        print(f"💡 备用代码: facebook")
        print(f"⌨️  按 Ctrl+C 退出")
    
    def save_totp_log(self, totp_info):
        """保存TOTP日志"""
        try:
            log_entry = {
                'timestamp': totp_info['timestamp'],
                'time': totp_info['current_time'],
                'code': totp_info['current_code'],
                'remaining_time': totp_info['remaining_time']
            }
            
            # 创建日志目录
            os.makedirs("facebook_2fa_results", exist_ok=True)
            
            # 保存到日志文件
            log_file = "facebook_2fa_results/totp_log.json"
            
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            else:
                logs = []
            
            # 只保存新的验证码
            if not logs or logs[-1]['code'] != totp_info['current_code']:
                logs.append(log_entry)
                
                # 只保留最近100条记录
                if len(logs) > 100:
                    logs = logs[-100:]
                
                with open(log_file, 'w', encoding='utf-8') as f:
                    json.dump(logs, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            pass  # 静默处理日志错误
    
    def run_realtime_display(self):
        """运行实时显示"""
        print("🚀 启动实时TOTP显示...")
        print("⏳ 正在初始化...")
        time.sleep(1)
        
        try:
            while True:
                # 清屏并显示信息
                self.clear_screen()
                self.display_header()
                
                # 生成TOTP信息
                totp_info = self.generate_totp_detailed()
                
                # 显示信息
                self.display_totp_info(totp_info)
                
                # 保存日志
                if totp_info:
                    self.save_totp_log(totp_info)
                
                # 等待1秒
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.clear_screen()
            print("🔑 Facebook TOTP工具")
            print("=" * 30)
            
            # 显示最后一次的验证码
            final_totp = self.generate_totp_detailed()
            if final_totp:
                print(f"🔑 最终验证码: {final_totp['current_code']}")
                print(f"⏰ 剩余时间: {final_totp['remaining_time']} 秒")
                print(f"⏭️  下个验证码: {final_totp['next_code']}")
            
            print(f"\n📋 使用说明:")
            print(f"1. 复制验证码到Facebook登录页面")
            print(f"2. 如需新验证码，重新运行此程序")
            print(f"3. 备用代码: facebook")
            
            print(f"\n📁 日志已保存到: facebook_2fa_results/totp_log.json")
            print(f"👋 程序已退出")
    
    def get_single_code(self):
        """获取单个验证码"""
        totp_info = self.generate_totp_detailed()
        if totp_info:
            print(f"🔑 当前验证码: {totp_info['current_code']}")
            print(f"⏰ 剩余时间: {totp_info['remaining_time']} 秒")
            print(f"⏭️  下个验证码: {totp_info['next_code']}")
            return totp_info['current_code']
        return None

def main():
    """主函数"""
    print("🔑 Facebook 实时TOTP验证码工具")
    print("=" * 40)
    print("选择模式:")
    print("1. 实时显示模式 (推荐)")
    print("2. 单次获取模式")
    print("0. 退出")
    
    choice = input("\n请选择 (0-2): ").strip()
    
    tool = RealTimeTOTPTool()
    
    if choice == "1":
        tool.run_realtime_display()
    elif choice == "2":
        tool.get_single_code()
    elif choice == "0":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
