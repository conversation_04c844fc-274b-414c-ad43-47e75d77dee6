#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UIAutomator2连接和基本功能
"""

import uiautomator2 as u2
import time

def test_device_connection():
    """测试设备连接"""
    print("🔍 测试设备连接...")
    
    # 测试连接到第一个设备
    try:
        d1 = u2.connect('127.0.0.1:5557')
        print(f"✅ 设备1连接成功: {d1.info.get('model', 'Unknown')}")
        
        # 获取设备信息
        info = d1.info
        print(f"   产品: {info.get('product', 'Unknown')}")
        print(f"   版本: {info.get('version', 'Unknown')}")
        print(f"   分辨率: {info.get('display', {}).get('width', 'Unknown')} x {info.get('display', {}).get('height', 'Unknown')}")
        
        # 测试截图
        print("📸 测试截图功能...")
        d1.screenshot("test_screenshot_device1.png")
        print("✅ 截图保存成功: test_screenshot_device1.png")
        
        return d1
        
    except Exception as e:
        print(f"❌ 设备1连接失败: {e}")
        return None

def test_device2_connection():
    """测试第二个设备连接"""
    print("\n🔍 测试第二个设备连接...")
    
    try:
        d2 = u2.connect('emulator-5556')
        print(f"✅ 设备2连接成功: {d2.info.get('model', 'Unknown')}")
        
        # 获取设备信息
        info = d2.info
        print(f"   产品: {info.get('product', 'Unknown')}")
        print(f"   版本: {info.get('version', 'Unknown')}")
        print(f"   分辨率: {info.get('display', {}).get('width', 'Unknown')} x {info.get('display', {}).get('height', 'Unknown')}")
        
        # 测试截图
        print("📸 测试截图功能...")
        d2.screenshot("test_screenshot_device2.png")
        print("✅ 截图保存成功: test_screenshot_device2.png")
        
        return d2
        
    except Exception as e:
        print(f"❌ 设备2连接失败: {e}")
        return None

def test_basic_operations(device, device_name):
    """测试基本操作"""
    if not device:
        return
    
    print(f"\n🎮 测试{device_name}基本操作...")
    
    try:
        # 测试点击屏幕中心
        width, height = device.window_size()
        center_x, center_y = width // 2, height // 2
        print(f"点击屏幕中心: ({center_x}, {center_y})")
        device.click(center_x, center_y)
        time.sleep(1)
        
        # 测试按键
        print("测试Home键...")
        device.press("home")
        time.sleep(1)
        
        # 测试滑动
        print("测试滑动操作...")
        device.swipe(width // 2, height // 2, width // 2, height // 4, duration=0.5)
        time.sleep(1)
        
        print(f"✅ {device_name}基本操作测试完成")
        
    except Exception as e:
        print(f"❌ {device_name}操作测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始UIAutomator2连接测试...")
    print("=" * 50)
    
    # 测试设备连接
    device1 = test_device_connection()
    device2 = test_device2_connection()
    
    # 测试基本操作
    if device1:
        test_basic_operations(device1, "设备1")
    
    if device2:
        test_basic_operations(device2, "设备2")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    if device1 or device2:
        print("\n📋 可用功能:")
        print("1. 浏览器控制界面: http://localhost:8080")
        print("2. 截图文件已保存到当前目录")
        print("3. 设备已准备好进行自动化操作")
        
        print("\n💡 使用示例:")
        print("```python")
        print("import uiautomator2 as u2")
        print("d = u2.connect('127.0.0.1:5557')  # 连接设备")
        print("d.click(100, 200)  # 点击坐标")
        print("d.screenshot('screenshot.png')  # 截图")
        print("d.press('home')  # 按Home键")
        print("```")
    else:
        print("❌ 没有可用设备，请检查连接")

if __name__ == "__main__":
    main()
