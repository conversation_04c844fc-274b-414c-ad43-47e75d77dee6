{"emulator": "emulator-2", "account_info": {"emulator": "emulator-2", "user_id": "未知", "username": "Facebook用户_模拟器2", "status": "已搜索"}, "totp_keys": [{"key": "AAAAEAAAAAAQAAAAEAAAAEAAAA", "formatted": "AAAA EAAA AAAQ AAAA EAAA AEAA AA", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "AAAAEAAAAAAQAAAAMAAAAEAAAA", "formatted": "AAAA EAAA AAAQ AAAA MAAA AEAA AA", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "AAAAEAAAAAAQAAAAAAAAAABAAA", "formatted": "AAAA EAAA AAAQ AAAA AAAA AABA AA", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "HQBAQAAAAAAAAAAAAABGAAAAFF", "formatted": "HQBA QAAA AAAA AAAA AABG AAAA FF", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "AAAAEAAAAAAQAAAAIAAAAEAAAA", "formatted": "AAAA EAAA AAAQ AAAA IAAA AEAA AA", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "AAAAAAAAAVU5TUEVDSUZJRUQAU", "formatted": "AAAA AAAA AVU5 TUEV DSUZ JRUQ AU", "source": "prefs_db", "type": "TOTP_26_BASE32"}, {"key": "MBAQEAAAAAAAAABAAAAAAAAABO", "formatted": "MBAQ EAAA AAAA AABA AAAA AAAA BO", "source": "prefs_db", "type": "TOTP_26_BASE32"}], "backup_codes": [{"code": "d4d805ba", "type": "8位十六进制备份代码", "source": "acra_criticaldata_store.xml"}, {"code": "625dd47d", "type": "8位十六进制备份代码", "source": "msys-auth-data.xml"}, {"code": "57791012", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "79389626", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "47746196", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "47225528", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "86456689", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "57483615", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "47704622", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "40626254", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "97677222", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "56588309", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "85596035", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "69260287", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "27828990", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "70011057", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "85596075", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "64998585", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "18843492", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}, {"code": "68761227", "type": "8位数字备份代码", "source": "rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml"}], "files_checked": ["/data/data/com.facebook.katana/shared_prefs/APP_TAGGING_PREFERENCES.xml", "/data/data/com.facebook.katana/shared_prefs/WebViewChromiumPrefs.xml", "/data/data/com.facebook.katana/shared_prefs/acra_criticaldata_store.xml", "/data/data/com.facebook.katana/shared_prefs/act_dns_cache.xml", "/data/data/com.facebook.katana/shared_prefs/com.facebook.secure.switchoff.xml", "/data/data/com.facebook.katana/shared_prefs/com.google.android.gms.appid.xml", "/data/data/com.facebook.katana/shared_prefs/fb_ard_nmlml_migration_version_schema.xml", "/data/data/com.facebook.katana/shared_prefs/lacrima.xml", "/data/data/com.facebook.katana/shared_prefs/large_heap_override_store.xml", "/data/data/com.facebook.katana/shared_prefs/mqtt_stickiness_controller.xml", "/data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml", "/data/data/com.facebook.katana/shared_prefs/msys-preferences.xml", "/data/data/com.facebook.katana/shared_prefs/papaya.xml", "/data/data/com.facebook.katana/shared_prefs/rti.mqtt.analytics.xml", "/data/data/com.facebook.katana/shared_prefs/rti.mqtt.fbns_notification_store_MqttLite_FBNS.xml", "/data/data/com.facebook.katana/shared_prefs/terminate_handler_flags_store.xml", "/data/data/com.facebook.katana/databases/OnDemandResources.db", "/data/data/com.facebook.katana/databases/OnDemandResources.db-journal", "/data/data/com.facebook.katana/databases/RKStorage", "/data/data/com.facebook.katana/databases/RKStorage-shm", "/data/data/com.facebook.katana/databases/RKStorage-wal", "/data/data/com.facebook.katana/databases/ads_db", "/data/data/com.facebook.katana/databases/ads_db-journal", "/data/data/com.facebook.katana/databases/client_message_push_dedup_db", "/data/data/com.facebook.katana/databases/client_message_push_dedup_db-shm", "/data/data/com.facebook.katana/databases/client_message_push_dedup_db-wal", "/data/data/com.facebook.katana/databases/composer_db", "/data/data/com.facebook.katana/databases/composer_db-journal", "/data/data/com.facebook.katana/databases/logdb_persistence", "/data/data/com.facebook.katana/databases/logdb_persistence-journal", "/data/data/com.facebook.katana/databases/mds_cache_db", "/data/data/com.facebook.katana/databases/mds_cache_db-journal", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01.db", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01.db-selfcheck", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01.db-shm", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01.db-wal", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01_activity_0.dat", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01_activity_1.dat", "/data/data/com.facebook.katana/databases/omnistore_61562522186148_v01_status.dat", "/data/data/com.facebook.katana/databases/prefs_db", "/data/data/com.facebook.katana/databases/prefs_db-journal", "/data/data/com.facebook.katana/databases/savedvideos.db", "/data/data/com.facebook.katana/databases/savedvideos.db-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.admined_pages_db_scoped", "/data/data/com.facebook.katana/databases/ssus.61562522186148.admined_pages_db_scoped-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.android_facebook_contacts_db", "/data/data/com.facebook.katana/databases/ssus.61562522186148.android_facebook_contacts_db-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.android_facebook_newsfeed_db", "/data/data/com.facebook.katana/databases/ssus.61562522186148.android_facebook_newsfeed_db-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.graph_cursors_uid", "/data/data/com.facebook.katana/databases/ssus.61562522186148.graph_cursors_uid-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.offline_mode_db_scoped", "/data/data/com.facebook.katana/databases/ssus.61562522186148.offline_mode_db_scoped-journal", "/data/data/com.facebook.katana/databases/ssus.61562522186148.search_bootstrap_db_uid", "/data/data/com.facebook.katana/databases/ssus.61562522186148.search_bootstrap_db_uid-journal", "/data/data/com.facebook.katana/databases/ssus.__out_of_scope__.offline_mode_db_scoped", "/data/data/com.facebook.katana/databases/ssus.__out_of_scope__.offline_mode_db_scoped-journal", "/data/data/com.facebook.katana/databases/time_in_app_61562522186148.db", "/data/data/com.facebook.katana/databases/time_in_app_61562522186148.db-shm", "/data/data/com.facebook.katana/databases/time_in_app_61562522186148.db-wal", "/data/data/com.facebook.katana/files/DefaultFrscUnpacker.lock", "/data/data/com.facebook.katana/files/FB4ARNBundle.lock", "/data/data/com.facebook.katana/files/GraphServiceUnpacker.lock", "/data/data/com.facebook.katana/files/crash_count", "/data/data/com.facebook.katana/files/fbdns.store", "/data/data/com.facebook.katana/files/fbfizz.store", "/data/data/com.facebook.katana/files/fbquic-fizz.store", "/data/data/com.facebook.katana/files/fbquic-token.store", "/data/data/com.facebook.katana/files/fbtlsx.store", "/data/data/com.facebook.katana/files/profileInstalled", "/data/data/com.facebook.katana/files/profileinstaller_profileWrittenFor_lastUpdateTime.dat", "/data/data/com.facebook.katana/files/risky_startup_config", "/data/data/com.facebook.katana/files/startup_experiments", "/data/data/com.facebook.katana/files/unpackLayoutBundle.lock", "/data/data/com.facebook.katana/cache/MqttDnsCache.store", "/data/data/com.facebook.katana/cache/MqttFizzCache.store", "/data/data/com.facebook.katana/cache/fontResourceCache.json", "/data/data/com.facebook.katana/cache/mqtt_log_event.idx", "/data/data/com.facebook.katana/cache/mqtt_log_event0.txt", "/data/data/com.facebook.katana/cache/mqtt_log_event1.txt", "/data/data/com.facebook.katana/cache/mqtt_log_event2.txt", "/data/data/com.facebook.katana/cache/mqtt_log_event3.txt", "/data/data/com.facebook.katana/cache/mqtt_log_event4.txt", "/data/data/com.facebook.katana/cache/notification_log_event.idx", "/data/data/com.facebook.katana/cache/notification_log_event0.txt", "/data/data/com.facebook.katana/cache/notification_log_event1.txt", "/data/data/com.facebook.katana/cache/notification_log_event2.txt", "/data/data/com.facebook.katana/cache/notification_log_event3.txt", "/data/data/com.facebook.katana/cache/notification_log_event4.txt", "/data/data/com.facebook.katana/cache/video-performance-tracking.data"]}