#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取当前UI状态
"""

import uiautomator2 as u2
import xml.etree.ElementTree as ET
import re

def get_current_ui():
    """获取当前UI状态"""
    try:
        # 连接设备
        d = u2.connect('127.0.0.1:5557')
        print("✅ 设备连接成功")
        
        # 获取当前应用信息
        current_app = d.app_current()
        print(f"📱 当前应用: {current_app}")
        
        # 获取UI层次结构
        xml_content = d.dump_hierarchy()
        print(f"📄 UI层次结构长度: {len(xml_content)} 字符")
        
        # 保存到文件
        with open("current_ui.xml", "w", encoding="utf-8") as f:
            f.write(xml_content)
        print("✅ UI层次结构已保存到 current_ui.xml")
        
        # 解析XML并查找所有可点击元素
        root = ET.fromstring(xml_content)
        
        clickable_elements = []
        
        def find_clickable_elements(element, depth=0):
            bounds = element.get('bounds', '')
            if bounds and element.get('clickable') == 'true':
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    
                    clickable_elements.append({
                        'center': (center_x, center_y),
                        'bounds': bounds,
                        'class': element.get('class', ''),
                        'text': element.get('text', ''),
                        'resource-id': element.get('resource-id', ''),
                        'content-desc': element.get('content-desc', ''),
                        'depth': depth
                    })
            
            for child in element:
                find_clickable_elements(child, depth + 1)
        
        find_clickable_elements(root)
        
        print(f"\n🎯 发现 {len(clickable_elements)} 个可点击元素:")
        
        for i, elem in enumerate(clickable_elements[:10]):  # 显示前10个
            print(f"\n元素 {i+1}:")
            print(f"  中心坐标: {elem['center']}")
            print(f"  边界: {elem['bounds']}")
            print(f"  类名: {elem['class']}")
            print(f"  文本: '{elem['text']}'")
            print(f"  资源ID: {elem['resource-id']}")
            print(f"  描述: {elem['content-desc']}")
        
        if len(clickable_elements) > 10:
            print(f"\n... 还有 {len(clickable_elements) - 10} 个元素")
        
        # 查找所有有边界的元素（不仅仅是可点击的）
        all_elements = []
        
        def find_all_elements(element, depth=0):
            bounds = element.get('bounds', '')
            if bounds:
                match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if match:
                    x1, y1, x2, y2 = map(int, match.groups())
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2
                    
                    all_elements.append({
                        'center': (center_x, center_y),
                        'bounds': bounds,
                        'class': element.get('class', ''),
                        'text': element.get('text', ''),
                        'clickable': element.get('clickable', 'false'),
                        'depth': depth
                    })
            
            for child in element:
                find_all_elements(child, depth + 1)
        
        find_all_elements(root)
        print(f"\n📊 总共发现 {len(all_elements)} 个有边界的元素")
        
        # 测试几个坐标点
        test_points = [(50, 50), (100, 100), (150, 150), (200, 200)]
        print(f"\n🧪 测试坐标点:")
        
        for x, y in test_points:
            matching = [elem for elem in all_elements 
                       if elem['bounds'] and 
                       re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', elem['bounds']) and
                       (lambda m: int(m.group(1)) <= x <= int(m.group(3)) and 
                                 int(m.group(2)) <= y <= int(m.group(4)))(re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', elem['bounds']))]
            
            if matching:
                deepest = max(matching, key=lambda e: e['depth'])
                print(f"  ({x}, {y}): ✅ {deepest['class']} - '{deepest['text']}'")
            else:
                print(f"  ({x}, {y}): ❌ 未找到")
        
        return clickable_elements
        
    except Exception as e:
        print(f"❌ 获取UI失败: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    get_current_ui()
