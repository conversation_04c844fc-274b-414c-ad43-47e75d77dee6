#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GuerrillaMail邮箱生成器
专门生成guerrillamail.com类型的临时邮箱
"""

import requests
import json
import time
import random
import string
import re
import os
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class GuerrillaMailGenerator:
    def __init__(self):
        """初始化GuerrillaMail生成器"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.session.verify = False
        
        self.facebook_user_id = "61562578582522"
        self.backup_code = "facebook"
        
        # GuerrillaMail配置
        self.guerrilla_config = {
            'base_url': 'https://api.guerrillamail.com/ajax.php',
            'domains': [
                'guerrillamail.com',
                'guerrillamail.net', 
                'guerrillamail.org',
                'guerrillamail.biz',
                'guerrillamail.de',
                'grr.la',
                'guerrillamailblock.com'
            ]
        }
        
        # 生成的邮箱列表
        self.generated_emails = []

        # 历史邮箱文件
        self.history_file = 'facebook_2fa_results/guerrilla_history.json'
        self.email_history = self.load_email_history()
    
    def load_email_history(self):
        """加载邮箱历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"📚 加载历史邮箱: {len(data.get('emails', []))} 个")
                return data
            else:
                return {'emails': [], 'created_time': datetime.now().isoformat()}
        except Exception as e:
            print(f"❌ 加载历史失败: {e}")
            return {'emails': [], 'created_time': datetime.now().isoformat()}

    def save_email_history(self):
        """保存邮箱历史记录"""
        try:
            # 确保目录存在
            import os
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)

            # 更新历史记录
            for email_info in self.generated_emails:
                # 检查是否已存在
                existing = False
                for hist_email in self.email_history['emails']:
                    if hist_email['email'] == email_info['email']:
                        # 更新现有记录
                        hist_email.update(email_info)
                        existing = True
                        break

                if not existing:
                    self.email_history['emails'].append(email_info)

            self.email_history['last_updated'] = datetime.now().isoformat()

            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.email_history, f, ensure_ascii=False, indent=2)

            print(f"✅ 历史记录已保存: {len(self.email_history['emails'])} 个邮箱")
            return True
        except Exception as e:
            print(f"❌ 保存历史失败: {e}")
            return False

    def generate_username(self, length=10):
        """生成随机用户名"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
    
    def create_guerrilla_email_api(self):
        """使用API创建GuerrillaMail邮箱"""
        try:
            print("📧 使用GuerrillaMail API创建邮箱...")
            
            # 获取新的邮箱地址
            params = {
                'f': 'get_email_address',
                'lang': 'en'
            }
            
            response = self.session.get(self.guerrilla_config['base_url'], params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if 'email_addr' in data:
                    email = data['email_addr']
                    sid_token = data.get('sid_token', '')
                    
                    email_info = {
                        'email': email,
                        'username': email.split('@')[0],
                        'domain': email.split('@')[1],
                        'api': 'guerrilla_api',
                        'sid_token': sid_token,
                        'created_time': datetime.now().strftime('%H:%M:%S'),
                        'status': 'active',
                        'messages': []
                    }
                    
                    print(f"✅ API创建成功: {email}")
                    return email_info
                else:
                    print(f"❌ API响应格式错误")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ API创建失败: {str(e)[:50]}...")
        
        return None
    
    def create_guerrilla_email_manual(self):
        """手动生成GuerrillaMail格式邮箱"""
        domain = random.choice(self.guerrilla_config['domains'])
        username = self.generate_username()
        email = f"{username}@{domain}"
        
        email_info = {
            'email': email,
            'username': username,
            'domain': domain,
            'api': 'guerrilla_manual',
            'created_time': datetime.now().strftime('%H:%M:%S'),
            'status': 'manual',
            'messages': []
        }
        
        print(f"✅ 手动生成: {email}")
        return email_info
    
    def create_multiple_guerrilla_emails(self, count=5):
        """创建多个GuerrillaMail邮箱"""
        print(f"🎯 创建 {count} 个GuerrillaMail邮箱...")
        print("=" * 60)
        
        created_emails = []
        
        # 先尝试API方式
        api_count = min(2, count)  # 最多2个API邮箱
        for i in range(api_count):
            email_info = self.create_guerrilla_email_api()
            if email_info:
                created_emails.append(email_info)
                self.generated_emails.append(email_info)
            time.sleep(2)  # API请求间隔
        
        # 剩余用手动生成
        manual_count = count - len(created_emails)
        for i in range(manual_count):
            email_info = self.create_guerrilla_email_manual()
            created_emails.append(email_info)
            self.generated_emails.append(email_info)
        
        # 保存历史记录
        self.save_email_history()

        print(f"\n✅ 总共创建 {len(created_emails)} 个GuerrillaMail邮箱")
        print(f"   API创建: {len([e for e in created_emails if e['api'] == 'guerrilla_api'])}")
        print(f"   手动生成: {len([e for e in created_emails if e['api'] == 'guerrilla_manual'])}")

        return created_emails
    
    def check_guerrilla_inbox_api(self, email_info):
        """使用API检查GuerrillaMail收件箱"""
        try:
            if 'sid_token' not in email_info:
                return []
            
            params = {
                'f': 'get_email_list',
                'offset': 0,
                'sid_token': email_info['sid_token']
            }
            
            response = self.session.get(self.guerrilla_config['base_url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('list', [])
                
        except Exception as e:
            print(f"❌ API检查失败: {str(e)[:30]}...")
        
        return []
    
    def get_guerrilla_message_content(self, email_info, msg_id):
        """获取GuerrillaMail邮件内容"""
        try:
            if 'sid_token' not in email_info:
                return ''
            
            params = {
                'f': 'fetch_email',
                'email_id': msg_id,
                'sid_token': email_info['sid_token']
            }
            
            response = self.session.get(self.guerrilla_config['base_url'], params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return data.get('mail_body', '')
                
        except:
            pass
        
        return ''
    
    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        patterns = [
            r'验证码[：:]\s*(\d{4,8})',
            r'verification code[：:]\s*(\d{4,8})',
            r'code[：:]\s*(\d{4,8})',
            r'(\d{6})',  # 6位数字
            r'(\d{4})',  # 4位数字
            r'(\d{8})',  # 8位数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                return matches[0]
        
        return None
    
    def monitor_guerrilla_emails(self, duration=300):
        """监控GuerrillaMail邮箱"""
        api_emails = [e for e in self.generated_emails if e['api'] == 'guerrilla_api']
        
        if not api_emails:
            print("⚠️ 没有可监控的API邮箱")
            return 0
        
        print(f"\n📬 开始监控 {len(api_emails)} 个GuerrillaMail邮箱...")
        print(f"⏰ 监控时长: {duration}秒")
        print("=" * 60)
        
        start_time = time.time()
        check_count = 0
        total_messages = 0
        
        while time.time() - start_time < duration:
            check_count += 1
            remaining_time = int(duration - (time.time() - start_time))
            
            print(f"\r🔄 检查 #{check_count} - 剩余时间: {remaining_time}秒", end='', flush=True)
            
            for email_info in api_emails:
                try:
                    messages = self.check_guerrilla_inbox_api(email_info)
                    
                    if messages:
                        new_messages = []
                        
                        for msg in messages:
                            msg_id = msg.get('mail_id', '')
                            existing_ids = [m.get('id') for m in email_info.get('messages', [])]
                            
                            if msg_id not in existing_ids:
                                sender = msg.get('mail_from', 'Unknown')
                                subject = msg.get('mail_subject', 'No Subject')
                                
                                print(f"\n🎉 {email_info['email']} 收到新邮件!")
                                print(f"   发件人: {sender}")
                                print(f"   主题: {subject}")
                                
                                # 获取邮件内容
                                content = self.get_guerrilla_message_content(email_info, msg_id)
                                
                                # 提取验证码
                                code = self.extract_verification_code(content) if content else None
                                
                                if code:
                                    print(f"   🔑 验证码: {code}")
                                
                                # 保存邮件信息
                                message_info = {
                                    'id': msg_id,
                                    'from': sender,
                                    'subject': subject,
                                    'content': content[:200] + '...' if content and len(content) > 200 else content,
                                    'verification_code': code,
                                    'received_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                }
                                
                                new_messages.append(message_info)
                                total_messages += 1
                        
                        # 更新邮箱信息
                        if new_messages:
                            if 'messages' not in email_info:
                                email_info['messages'] = []
                            email_info['messages'].extend(new_messages)
                
                except Exception as e:
                    continue
            
            time.sleep(10)  # 每10秒检查一次
        
        print(f"\n⏰ 监控结束，共收到 {total_messages} 条新邮件")
        return total_messages
    
    def display_guerrilla_emails(self):
        """显示生成的GuerrillaMail邮箱"""
        print(f"\n📧 当前活跃的GuerrillaMail邮箱:")
        print("=" * 60)

        if not self.generated_emails:
            print("❌ 没有活跃的邮箱")
            return

        for i, email_info in enumerate(self.generated_emails, 1):
            status_icon = "🟢" if email_info['api'] == 'guerrilla_api' else "🔵"
            message_count = len(email_info.get('messages', []))

            print(f"{i:2d}. {status_icon} {email_info['email']}")
            print(f"     类型: {email_info['api']}")
            print(f"     域名: {email_info['domain']}")
            print(f"     创建时间: {email_info['created_time']}")
            print(f"     邮件数量: {message_count}")

            # 显示最新邮件
            messages = email_info.get('messages', [])
            if messages:
                latest = messages[-1]
                print(f"     最新邮件: {latest.get('subject', 'No Subject')}")
                if latest.get('verification_code'):
                    print(f"     验证码: {latest['verification_code']}")
            print()

    def display_email_history(self):
        """显示邮箱历史记录"""
        print(f"\n📚 GuerrillaMail邮箱历史记录:")
        print("=" * 60)

        emails = self.email_history.get('emails', [])

        if not emails:
            print("❌ 没有历史邮箱记录")
            return

        for i, email_info in enumerate(emails, 1):
            status_icon = "🟢" if email_info.get('api') == 'guerrilla_api' else "🔵"
            message_count = len(email_info.get('messages', []))

            print(f"{i:2d}. {status_icon} {email_info['email']}")
            print(f"     类型: {email_info.get('api', 'unknown')}")
            print(f"     域名: {email_info.get('domain', 'unknown')}")
            print(f"     创建时间: {email_info.get('created_time', 'Unknown')}")
            print(f"     邮件数量: {message_count}")

            # 显示最新邮件
            messages = email_info.get('messages', [])
            if messages:
                latest = messages[-1]
                print(f"     最新邮件: {latest.get('subject', 'No Subject')}")
                if latest.get('verification_code'):
                    print(f"     验证码: {latest['verification_code']}")
            print()

    def select_from_history(self):
        """从历史中选择邮箱"""
        emails = self.email_history.get('emails', [])

        if not emails:
            print("❌ 没有历史邮箱可选择")
            return []

        print(f"\n📋 选择历史邮箱进行监控:")
        print("=" * 50)

        for i, email_info in enumerate(emails, 1):
            status_icon = "🟢" if email_info.get('api') == 'guerrilla_api' else "🔵"
            message_count = len(email_info.get('messages', []))
            print(f"{i:2d}. {status_icon} {email_info['email']} ({message_count} 邮件)")

        print("0. 全部选择")

        choice = input(f"\n请选择邮箱 (1-{len(emails)}, 0=全部, 多个用逗号分隔): ").strip()

        selected = []

        if choice == "0":
            selected = emails.copy()
        else:
            try:
                indices = [int(x.strip()) for x in choice.split(',') if x.strip().isdigit()]
                for idx in indices:
                    if 1 <= idx <= len(emails):
                        selected.append(emails[idx - 1])
            except:
                print("❌ 输入格式错误")
                return []

        # 添加到当前活跃列表
        for email_info in selected:
            if email_info not in self.generated_emails:
                self.generated_emails.append(email_info)

        print(f"✅ 已选择 {len(selected)} 个历史邮箱加入监控")
        return selected
    
    def save_guerrilla_data(self):
        """保存GuerrillaMail数据"""
        try:
            data = {
                'facebook_user_id': self.facebook_user_id,
                'backup_code': self.backup_code,
                'guerrilla_emails': self.generated_emails,
                'created_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'summary': {
                    'total_emails': len(self.generated_emails),
                    'api_emails': len([e for e in self.generated_emails if e['api'] == 'guerrilla_api']),
                    'manual_emails': len([e for e in self.generated_emails if e['api'] == 'guerrilla_manual']),
                    'domains': list(set([e['domain'] for e in self.generated_emails]))
                }
            }
            
            filename = f"facebook_2fa_results/guerrilla_emails_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ GuerrillaMail数据已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    
    def provide_usage_guide(self):
        """提供使用指南"""
        print(f"\n🎯 GuerrillaMail使用指南:")
        print("=" * 50)
        
        print("📋 GuerrillaMail特点:")
        print("✅ 1小时有效期")
        print("✅ 支持API自动监控")
        print("✅ 多个域名可选")
        print("✅ 无需注册")
        
        print(f"\n📋 Facebook使用步骤:")
        print("1. 访问: https://www.facebook.com/login/identify")
        print("2. 输入生成的GuerrillaMail邮箱")
        print("3. 点击发送重置邮件")
        print("4. 程序自动监控并显示验证码")
        
        print(f"\n📋 手动检查方法:")
        print("1. 访问: https://www.guerrillamail.com/")
        print("2. 输入邮箱地址查看收件箱")
        print("3. 查找Facebook发送的邮件")
        
        print(f"\n🔑 如果需要2FA验证:")
        print(f"   - 备份代码: {self.backup_code}")
        print(f"   - TOTP工具: python FBaf2提取/实时TOTP工具.py")
    
    def run_generator(self):
        """运行GuerrillaMail生成器"""
        print("🎯 GuerrillaMail邮箱生成器")
        print("专门生成guerrillamail.com类型的临时邮箱")
        print("=" * 60)
        
        while True:
            print(f"\n📋 选择操作:")
            print("1. 生成GuerrillaMail邮箱")
            print("2. 显示当前活跃邮箱")
            print("3. 从历史中选择邮箱")
            print("4. 显示邮箱历史记录")
            print("5. 开始监控邮件")
            print("6. 保存邮箱数据")
            print("7. 使用指南")
            print("0. 退出")

            choice = input("\n请选择 (0-7): ").strip()
            
            if choice == "1":
                count = input("生成邮箱数量 (默认5): ").strip()
                count = int(count) if count.isdigit() else 5
                self.create_multiple_guerrilla_emails(count)

            elif choice == "2":
                self.display_guerrilla_emails()

            elif choice == "3":
                self.select_from_history()

            elif choice == "4":
                self.display_email_history()

            elif choice == "5":
                if not self.generated_emails:
                    print("❌ 没有活跃的邮箱，请先生成邮箱或从历史中选择")
                    continue

                duration = input("监控时长(秒，默认300): ").strip()
                duration = int(duration) if duration.isdigit() else 300

                self.monitor_guerrilla_emails(duration)

            elif choice == "6":
                self.save_guerrilla_data()

            elif choice == "7":
                self.provide_usage_guide()

            elif choice == "0":
                print("👋 退出GuerrillaMail生成器")
                break

            else:
                print("❌ 无效选择")

def main():
    """主函数"""
    print("🎯 GuerrillaMail邮箱生成器")
    print("=" * 40)
    
    try:
        generator = GuerrillaMailGenerator()
        generator.run_generator()
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
