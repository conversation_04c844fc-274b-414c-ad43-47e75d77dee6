#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接2FA提取器 - 针对已root模拟器的简化版本
专门用于在确认有root权限的情况下直接提取Facebook 2FA数据
"""

import os
import sys
import re
import json
import base64
from datetime import datetime

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from 雷电API import Dnconsole

class Direct2FAExtractor:
    def __init__(self, emulator_index=1):
        """初始化直接2FA提取器"""
        # 雷电模拟器路径配置
        base_paths = [
            r"G:\leidian\LDPlayer9",
            r"G:\LDPlayer\LDPlayer9", 
            r"C:\LDPlayer\LDPlayer9",
            r"D:\LDPlayer\LDPlayer9",
            r"E:\LDPlayer\LDPlayer9"
        ]
        
        self.base_path = None
        for path in base_paths:
            if os.path.exists(os.path.join(path, "ld.exe")):
                self.base_path = path
                break
        
        if not self.base_path:
            raise FileNotFoundError("未找到雷电模拟器安装路径")
        
        self.share_path = os.path.expanduser("~/Documents/leidian64")
        self.emulator_index = emulator_index
        
        # 初始化雷电控制台
        self.ld = Dnconsole(
            base_path=self.base_path,
            share_path=self.share_path,
            emulator_id=emulator_index
        )
        
        print(f"🚀 直接2FA提取器初始化成功")
        print(f"📱 目标模拟器: {emulator_index}")

    def execute_root_command(self, command):
        """执行root命令"""
        try:
            # 直接使用su执行命令
            root_command = f'su -c "{command}"'
            success, output = self.ld.execute_ld(self.emulator_index, root_command)
            return output if success else None
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return None

    def check_facebook_status(self):
        """检查Facebook应用状态"""
        print("\n🔍 检查Facebook应用状态...")
        
        # 1. 检查应用是否安装
        result = self.execute_root_command("pm list packages | grep facebook")
        if not result or "com.facebook.katana" not in result:
            print("❌ Facebook应用未安装")
            return False
        print("✅ Facebook应用已安装")
        
        # 2. 检查应用是否运行
        result = self.execute_root_command("pidof com.facebook.katana")
        if result and result.strip():
            print(f"✅ Facebook应用正在运行 (PID: {result.strip()})")
        else:
            print("⚠️ Facebook应用未运行")
        
        # 3. 检查数据目录
        result = self.execute_root_command("ls -la /data/data/com.facebook.katana/")
        if result and "Permission denied" not in result:
            print("✅ 数据目录可访问")
            return True
        else:
            print("❌ 数据目录访问失败")
            return False

    def search_totp_in_file(self, file_path):
        """在指定文件中搜索TOTP密钥"""
        content = self.execute_root_command(f"cat '{file_path}'")
        if not content:
            return []
        
        found_keys = []
        
        # TOTP密钥模式
        patterns = [
            (r'\b[A-Z2-7]{26}\b', '26字符Base32'),
            (r'\b[A-Z2-7]{32}\b', '32字符Base32'),
            (r'\b[A-Z2-7]{20}\b', '20字符Base32'),
            (r'\b[a-fA-F0-9]{52}\b', '52字符十六进制'),
            (r'\b[a-fA-F0-9]{40}\b', '40字符十六进制'),
        ]
        
        for pattern, desc in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # 验证是否为有效的TOTP密钥
                if self.validate_totp_key(match):
                    found_keys.append({
                        'key': match,
                        'type': desc,
                        'file': file_path,
                        'context': self.get_context(content, match)
                    })
        
        return found_keys

    def validate_totp_key(self, key):
        """验证TOTP密钥有效性"""
        try:
            # 排除明显的英文单词
            exclude_words = [
                'FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'ANDROID', 'SYSTEM',
                'AMBIENT', 'DIFFUSE', 'STATIC', 'ENVIRONMENT', 'TEXTURE', 'ROTATION'
            ]
            
            key_upper = key.upper()
            for word in exclude_words:
                if word in key_upper:
                    return False
            
            # 检查字符重复度
            char_counts = {}
            for char in key:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            max_char_ratio = max(char_counts.values()) / len(key)
            if max_char_ratio > 0.4:  # 如果任何字符出现超过40%，可能不是真正的密钥
                return False
            
            # 尝试Base32解码（如果是Base32格式）
            if all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567' for c in key.upper()):
                try:
                    padded_key = key + '=' * (8 - len(key) % 8) % 8
                    decoded = base64.b32decode(padded_key)
                    return 10 <= len(decoded) <= 64
                except:
                    return False
            
            return True
            
        except:
            return False

    def get_context(self, content, target):
        """获取上下文"""
        try:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if target in line:
                    start = max(0, i - 2)
                    end = min(len(lines), i + 3)
                    return '\n'.join(lines[start:end])
            return target
        except:
            return target

    def extract_from_databases(self):
        """从数据库文件提取"""
        print("\n🗄️ 从数据库文件提取...")
        found_keys = []
        
        # 查找数据库文件
        db_result = self.execute_root_command("find /data/data/com.facebook.katana/ -name '*.db' -o -name '*.sqlite' -o -name '*.sqlite3'")
        
        if db_result:
            db_files = [f.strip() for f in db_result.split('\n') if f.strip()]
            print(f"发现 {len(db_files)} 个数据库文件")
            
            for db_file in db_files:
                print(f"  📊 分析: {os.path.basename(db_file)}")
                
                # 使用strings提取可读字符串
                strings_result = self.execute_root_command(f"strings '{db_file}'")
                if strings_result:
                    keys = self.search_totp_in_content(strings_result, f"数据库:{os.path.basename(db_file)}")
                    found_keys.extend(keys)
        
        return found_keys

    def search_totp_in_content(self, content, source):
        """在内容中搜索TOTP密钥"""
        found_keys = []
        
        patterns = [
            (r'\b[A-Z2-7]{26}\b', '26字符Base32'),
            (r'\b[A-Z2-7]{32}\b', '32字符Base32'),
            (r'\b[a-fA-F0-9]{52}\b', '52字符十六进制'),
        ]
        
        for pattern, desc in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if self.validate_totp_key(match):
                    found_keys.append({
                        'key': match,
                        'type': desc,
                        'source': source,
                        'context': self.get_context(content, match)
                    })
        
        return found_keys

    def run_direct_extraction(self):
        """运行直接提取"""
        print("🚀 开始直接2FA提取")
        print("=" * 50)
        
        # 1. 检查Facebook状态
        if not self.check_facebook_status():
            print("❌ Facebook应用状态检查失败")
            return False
        
        all_found_keys = []
        
        # 2. 搜索重要文件
        print("\n📄 搜索重要文件...")
        important_files = [
            "/data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml",
            "/data/data/com.facebook.katana/shared_prefs/com.facebook.secure.switchoff.xml",
            "/data/data/com.facebook.katana/shared_prefs/authentication.xml",
            "/data/data/com.facebook.katana/files/auth_data",
        ]
        
        for file_path in important_files:
            print(f"  📄 检查: {os.path.basename(file_path)}")
            keys = self.search_totp_in_file(file_path)
            if keys:
                print(f"    🔑 找到 {len(keys)} 个密钥")
                all_found_keys.extend(keys)
            else:
                print(f"    ❌ 未找到密钥")
        
        # 3. 搜索数据库
        db_keys = self.extract_from_databases()
        all_found_keys.extend(db_keys)
        
        # 4. 显示结果
        print(f"\n📊 提取结果:")
        print("=" * 30)
        print(f"总共找到: {len(all_found_keys)} 个可能的TOTP密钥")
        
        if all_found_keys:
            for i, key_info in enumerate(all_found_keys, 1):
                print(f"\n🔑 密钥 {i}:")
                print(f"   值: {key_info['key']}")
                print(f"   类型: {key_info['type']}")
                print(f"   来源: {key_info.get('source', key_info.get('file', '未知'))}")
        
        # 5. 保存结果
        if all_found_keys:
            self.save_results(all_found_keys)
            return True
        else:
            print("\n❌ 未找到任何TOTP密钥")
            return False

    def save_results(self, keys):
        """保存提取结果"""
        try:
            os.makedirs('direct_2fa_results', exist_ok=True)
            
            result_data = {
                'extraction_time': datetime.now().isoformat(),
                'emulator_index': self.emulator_index,
                'total_keys': len(keys),
                'keys': keys
            }
            
            filename = f"direct_2fa_results/direct_extract_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 结果已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")

def main():
    """主函数"""
    print("🚀 直接2FA提取器")
    print("=" * 30)
    
    try:
        extractor = Direct2FAExtractor(emulator_index=1)
        success = extractor.run_direct_extraction()
        
        if success:
            print("\n🎉 直接提取成功！")
        else:
            print("\n❌ 直接提取失败")
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
