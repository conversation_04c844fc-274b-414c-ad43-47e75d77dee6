#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟器2超级搜索器
使用所有可能的方法搜索TOTP密钥
"""

import os
import json
import subprocess
import re
import base64
import binascii
from datetime import datetime

class Emulator2SuperSearcher:
    def __init__(self):
        self.device_id = "emulator-5556"
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        self.results = {
            'emulator': 'emulator-2',
            'search_results': [],
            'totp_candidates': [],
            'all_findings': []
        }
        
    def execute_adb_command(self, command):
        """执行ADB命令"""
        try:
            full_command = f'"{self.adb_path}" -s {self.device_id} shell "su -c \'{command}\'"'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=180)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            return None
    
    def search_all_possible_patterns(self):
        """搜索所有可能的模式"""
        print("🔍 超级搜索 - 所有可能的TOTP模式")
        print("="*60)
        
        # 扩展的搜索模式
        search_patterns = [
            # 十六进制模式
            ("52字符十六进制", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null"),
            ("40字符十六进制", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{40\\}' {} \\; 2>/dev/null"),
            ("32字符十六进制", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{32\\}' {} \\; 2>/dev/null"),
            
            # Base32模式
            ("26字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null"),
            ("32字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null"),
            ("20字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{20\\}' {} \\; 2>/dev/null"),
            ("16字符Base32", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{16\\}' {} \\; 2>/dev/null"),
            
            # Base64模式
            ("Base64长", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{35,40\\}={0,2}' {} \\; 2>/dev/null"),
            ("Base64中", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{28,34\\}={0,2}' {} \\; 2>/dev/null"),
            ("Base64短", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{20,27\\}={0,2}' {} \\; 2>/dev/null"),
            
            # 混合模式
            ("长字母数字", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9]\\{20,32\\}' {} \\; 2>/dev/null"),
            ("纯数字长", "find /data/data/com.facebook.katana/ -type f -exec grep -o '[0-9]\\{16,32\\}' {} \\; 2>/dev/null"),
        ]
        
        for pattern_name, command in search_patterns:
            print(f"\n🔍 搜索: {pattern_name}")
            print(f"   命令: {command}")
            
            result = self.execute_adb_command(command)
            
            if result:
                candidates = [line.strip() for line in result.split('\n') if line.strip()]
                unique_candidates = list(set(candidates))
                
                print(f"   📊 发现 {len(candidates)} 个候选 (去重后 {len(unique_candidates)} 个)")
                
                search_result = {
                    'pattern': pattern_name,
                    'command': command,
                    'total_found': len(candidates),
                    'unique_found': len(unique_candidates),
                    'candidates': unique_candidates[:20]  # 只保存前20个
                }
                
                self.results['search_results'].append(search_result)
                
                # 分析每个候选
                for candidate in unique_candidates[:10]:  # 只分析前10个
                    self.analyze_candidate(candidate, pattern_name)
                    
            else:
                print(f"   ❌ 未找到")
                search_result = {
                    'pattern': pattern_name,
                    'command': command,
                    'total_found': 0,
                    'unique_found': 0,
                    'candidates': []
                }
                self.results['search_results'].append(search_result)
    
    def analyze_candidate(self, candidate, source_pattern):
        """分析候选密钥"""
        if not candidate or len(candidate) < 16:
            return
        
        print(f"      🔍 分析: {candidate[:20]}{'...' if len(candidate) > 20 else ''}")
        
        analysis = {
            'value': candidate,
            'length': len(candidate),
            'source_pattern': source_pattern,
            'analysis_results': []
        }
        
        # 1. 如果是十六进制，尝试转换为Base32
        if re.match(r'^[a-fA-F0-9]+$', candidate):
            base32_result = self.hex_to_base32(candidate)
            if base32_result:
                analysis['analysis_results'].append({
                    'method': 'hex_to_base32',
                    'result': base32_result,
                    'valid': self.validate_totp_key(base32_result)
                })
                if self.validate_totp_key(base32_result):
                    print(f"         ✅ 十六进制→Base32: {base32_result}")
                    self.add_totp_candidate(base32_result, f"十六进制转换({source_pattern})", candidate)
        
        # 2. 如果是Base64，尝试转换为Base32
        if re.match(r'^[A-Za-z0-9+/]+=*$', candidate):
            base32_result = self.base64_to_base32(candidate)
            if base32_result:
                analysis['analysis_results'].append({
                    'method': 'base64_to_base32',
                    'result': base32_result,
                    'valid': self.validate_totp_key(base32_result)
                })
                if self.validate_totp_key(base32_result):
                    print(f"         ✅ Base64→Base32: {base32_result}")
                    self.add_totp_candidate(base32_result, f"Base64转换({source_pattern})", candidate)
        
        # 3. 如果是Base32，直接验证
        if re.match(r'^[A-Z2-7]+$', candidate):
            if self.validate_totp_key(candidate):
                analysis['analysis_results'].append({
                    'method': 'direct_base32',
                    'result': candidate,
                    'valid': True
                })
                print(f"         ✅ 直接Base32: {candidate}")
                self.add_totp_candidate(candidate, f"直接Base32({source_pattern})")
        
        # 4. 尝试作为URL编码解码
        try:
            import urllib.parse
            decoded = urllib.parse.unquote(candidate)
            if decoded != candidate and len(decoded) >= 16:
                analysis['analysis_results'].append({
                    'method': 'url_decode',
                    'result': decoded,
                    'valid': False
                })
                # 递归分析解码后的结果
                self.analyze_candidate(decoded, f"URL解码({source_pattern})")
        except:
            pass
        
        self.results['all_findings'].append(analysis)
    
    def add_totp_candidate(self, key, source, original=None):
        """添加TOTP候选密钥"""
        candidate = {
            'key': key,
            'formatted': ' '.join([key[i:i+4] for i in range(0, len(key), 4)]),
            'source': source,
            'length': len(key),
            'confidence': self.calculate_confidence(key),
            'timestamp': datetime.now().isoformat()
        }
        
        if original:
            candidate['original'] = original
        
        # 检查是否已存在
        for existing in self.results['totp_candidates']:
            if existing['key'] == key:
                return
        
        self.results['totp_candidates'].append(candidate)
    
    def calculate_confidence(self, key):
        """计算密钥置信度"""
        score = 0
        
        # 长度检查
        if len(key) == 26:
            score += 30
        elif len(key) == 32:
            score += 25
        elif len(key) == 20:
            score += 20
        
        # 字符分布检查
        char_counts = {}
        for char in key:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # 字符多样性
        unique_chars = len(char_counts)
        if unique_chars >= 10:
            score += 20
        elif unique_chars >= 8:
            score += 15
        elif unique_chars >= 6:
            score += 10
        
        # 重复字符检查
        max_char_ratio = max(char_counts.values()) / len(key)
        if max_char_ratio <= 0.2:
            score += 20
        elif max_char_ratio <= 0.3:
            score += 15
        elif max_char_ratio <= 0.4:
            score += 10
        
        # 连续字符检查
        consecutive_count = 1
        max_consecutive = 1
        for i in range(1, len(key)):
            if key[i] == key[i-1]:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 1
        
        if max_consecutive <= 2:
            score += 15
        elif max_consecutive <= 3:
            score += 10
        
        # 排除明显的单词
        exclude_words = ['FACEBOOK', 'GOOGLE', 'ANDROID', 'SYSTEM']
        for word in exclude_words:
            if word in key.upper():
                score -= 30
        
        return min(100, max(0, score))
    
    def hex_to_base32(self, hex_string):
        """十六进制转Base32"""
        try:
            hex_clean = hex_string.replace(' ', '').lower()
            if not re.match(r'^[0-9a-f]+$', hex_clean):
                return None
            
            # 确保长度为偶数
            if len(hex_clean) % 2 != 0:
                return None
            
            bytes_data = binascii.unhexlify(hex_clean)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def base64_to_base32(self, b64_string):
        """Base64转Base32"""
        try:
            # 添加填充
            padded = b64_string + '=' * (4 - len(b64_string) % 4) % 4
            bytes_data = base64.b64decode(padded)
            base32_string = base64.b32encode(bytes_data).decode('ascii').rstrip('=')
            return base32_string
        except:
            return None
    
    def validate_totp_key(self, key):
        """验证TOTP密钥"""
        try:
            if len(key) < 16 or len(key) > 32:
                return False
            
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # 尝试Base32解码
            padded = key + '=' * (8 - len(key) % 8) % 8
            decoded = base64.b32decode(padded)
            
            if len(decoded) < 10 or len(decoded) > 64:
                return False
            
            return True
        except:
            return False
    
    def search_specific_files(self):
        """搜索特定文件"""
        print(f"\n🔍 搜索特定认证文件...")
        
        specific_files = [
            "/data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml",
            "/data/data/com.facebook.katana/shared_prefs/com.facebook.secure.switchoff.xml",
            "/data/data/com.facebook.katana/shared_prefs/authentication.xml",
            "/data/data/com.facebook.katana/databases/prefs_db",
            "/data/data/com.facebook.katana/files/auth_data"
        ]
        
        for file_path in specific_files:
            print(f"   📄 检查: {os.path.basename(file_path)}")
            
            # 检查文件是否存在
            exists = self.execute_adb_command(f"test -f '{file_path}' && echo 'exists'")
            
            if exists:
                print(f"      ✅ 文件存在")
                
                # 读取文件内容
                content = self.execute_adb_command(f"cat '{file_path}' 2>/dev/null")
                if content:
                    print(f"      📖 文件大小: {len(content)} 字符")
                    
                    # 在内容中搜索所有可能的密钥
                    self.search_in_content(content, os.path.basename(file_path))
                else:
                    print(f"      ❌ 无法读取文件内容")
            else:
                print(f"      ❌ 文件不存在")
    
    def search_in_content(self, content, source_file):
        """在内容中搜索密钥"""
        patterns = [
            (r'[a-fA-F0-9]{52}', '52字符十六进制'),
            (r'[a-fA-F0-9]{40}', '40字符十六进制'),
            (r'[a-fA-F0-9]{32}', '32字符十六进制'),
            (r'[A-Z2-7]{26}', '26字符Base32'),
            (r'[A-Z2-7]{32}', '32字符Base32'),
            (r'[A-Za-z0-9+/]{28,40}={0,2}', 'Base64编码'),
        ]
        
        for pattern, pattern_name in patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"         🔍 {pattern_name}: 发现 {len(matches)} 个匹配")
                for match in matches[:5]:  # 只处理前5个
                    self.analyze_candidate(match, f"{source_file}:{pattern_name}")
    
    def display_results(self):
        """显示结果"""
        print("\n" + "="*60)
        print("📊 模拟器2超级搜索结果")
        print("="*60)
        
        # 搜索摘要
        print("🔍 搜索摘要:")
        total_patterns = len(self.results['search_results'])
        successful_patterns = len([r for r in self.results['search_results'] if r['total_found'] > 0])
        
        print(f"   总搜索模式: {total_patterns}")
        print(f"   成功模式: {successful_patterns}")
        print(f"   总候选数: {sum(r['total_found'] for r in self.results['search_results'])}")
        
        # TOTP候选
        if self.results['totp_candidates']:
            print(f"\n🔑 TOTP候选密钥 ({len(self.results['totp_candidates'])}个):")
            
            # 按置信度排序
            sorted_candidates = sorted(self.results['totp_candidates'], 
                                     key=lambda x: x['confidence'], reverse=True)
            
            for i, candidate in enumerate(sorted_candidates, 1):
                print(f"   {i}. {candidate['key']}")
                print(f"      格式化: {candidate['formatted']}")
                print(f"      来源: {candidate['source']}")
                print(f"      置信度: {candidate['confidence']}/100")
                if 'original' in candidate:
                    print(f"      原始数据: {candidate['original']}")
                print()
        else:
            print("\n❌ 未发现TOTP候选密钥")
        
        # 详细搜索结果
        print("📋 详细搜索结果:")
        for result in self.results['search_results']:
            if result['total_found'] > 0:
                print(f"   ✅ {result['pattern']}: {result['unique_found']} 个唯一结果")
            else:
                print(f"   ❌ {result['pattern']}: 无结果")
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"facebook_2fa_results/emulator2_super_{timestamp}.json"
        
        os.makedirs("facebook_2fa_results", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def run_super_search(self):
        """运行超级搜索"""
        print("🎯 模拟器2超级搜索器")
        print("使用所有可能的方法搜索TOTP密钥")
        print("="*60)
        
        # 1. 搜索所有可能的模式
        self.search_all_possible_patterns()
        
        # 2. 搜索特定文件
        self.search_specific_files()
        
        # 3. 显示结果
        self.display_results()
        
        # 4. 保存结果
        filename = self.save_results()
        print(f"\n✅ 超级搜索结果已保存到: {filename}")
        
        return len(self.results['totp_candidates']) > 0

def main():
    """主函数"""
    searcher = Emulator2SuperSearcher()
    success = searcher.run_super_search()
    
    if success:
        print("\n💡 使用说明:")
        print("1. 优先使用置信度最高的密钥")
        print("2. 在验证器应用中添加新账户")
        print("3. 手动输入密钥")
        print("4. 账户名称: Facebook_模拟器2")
        print("5. 测试生成的验证码")
    else:
        print("\n💡 结论:")
        print("模拟器2中可能确实没有Facebook 2FA密钥")
        print("建议在Facebook应用中手动启用2FA")

if __name__ == "__main__":
    main()
