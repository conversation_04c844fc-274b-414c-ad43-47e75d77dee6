# Android自动化控制系统

## 🎉 连接成功！

您的Android设备已成功连接到UIAutomator2，现在可以通过浏览器进行操作了！

## 📱 已连接设备

- **设备1**: `127.0.0.1:5557` (网络连接)
- **设备2**: `emulator-5556` (雷电模拟器)

## 🌐 浏览器操作界面

**访问地址**: http://localhost:8080

### 功能特性

1. **实时屏幕显示** - 查看设备当前屏幕
2. **双模式操作** - 点击模式和检查模式
3. **UI元素检查** - 获取元素的详细属性信息
4. **坐标显示** - 鼠标悬停显示精确坐标
5. **快捷按键** - Home、Back、Menu、Recent按键
6. **滑动操作** - 上下左右滑动功能
7. **文本输入** - 向设备发送文本
8. **截图保存** - 保存当前屏幕截图
9. **设备信息** - 查看设备详细信息

### 使用方法

1. 打开浏览器访问 http://localhost:8080
2. 界面会自动显示设备屏幕
3. **点击模式**：直接点击屏幕进行操作
4. **检查模式**：点击元素查看详细信息（类似您图片中的功能）
5. 使用左侧控制面板进行快捷操作和滑动

### 🔍 UI元素检查功能

在检查模式下，点击任意UI元素可以获取：

- **XPath路径** - 元素的完整路径
- **class** - 元素类名 (如 android.widget.EditText)
- **resource-id** - 资源ID (如 com.instagram.android:id/row_thread_composer_edittext)
- **text** - 元素文本内容
- **bounds** - 元素边界坐标
- **activity** - 当前Activity
- **package** - 应用包名
- **各种属性** - clickable, enabled, focusable等

## 💻 编程接口

### 基本连接

```python
import uiautomator2 as u2

# 连接指定设备
d1 = u2.connect('127.0.0.1:5557')  # 网络设备
d2 = u2.connect('emulator-5556')   # 模拟器

# 自动连接第一个设备
d = u2.connect()
```

### 常用操作

```python
# 点击操作
d.click(x, y)                    # 点击坐标
d.click(0.5, 0.5)               # 点击屏幕中心(相对坐标)

# 文本操作
d.send_keys("Hello World")       # 输入文本
d.clear_text()                   # 清除文本

# 按键操作
d.press("home")                  # Home键
d.press("back")                  # 返回键
d.press("menu")                  # 菜单键
d.press("recent")                # 最近任务

# 滑动操作
d.swipe(x1, y1, x2, y2)         # 滑动
d.swipe_ext("up")               # 向上滑动
d.swipe_ext("down")             # 向下滑动

# 截图
d.screenshot("screenshot.png")   # 保存截图
img = d.screenshot(format='pillow')  # 获取PIL图像对象

# 设备信息
print(d.info)                   # 设备信息
print(d.window_size())          # 屏幕尺寸
```

### 元素定位

```python
# 通过文本定位
d(text="设置").click()

# 通过资源ID定位
d(resourceId="com.android.settings:id/title").click()

# 通过类名定位
d(className="android.widget.Button").click()

# 组合条件
d(text="设置", className="android.widget.TextView").click()
```

### 等待操作

```python
# 等待元素出现
d(text="设置").wait(timeout=10)

# 等待元素消失
d(text="加载中").wait_gone(timeout=10)

# 等待并点击
d(text="确定").wait().click()
```

## 🔧 高级功能

### 多设备管理

```python
class DeviceManager:
    def __init__(self):
        self.devices = {
            'device1': u2.connect('127.0.0.1:5557'),
            'device2': u2.connect('emulator-5556')
        }
    
    def operate_device(self, device_name, operation):
        device = self.devices.get(device_name)
        if device:
            operation(device)

# 使用示例
manager = DeviceManager()
manager.operate_device('device1', lambda d: d.click(100, 200))
```

### 异步操作

```python
import asyncio

async def async_operation(device):
    device.click(100, 200)
    await asyncio.sleep(1)
    device.press("home")

# 运行异步操作
asyncio.run(async_operation(d))
```

## 📁 文件说明

- `setup_android_automation.py` - 自动安装脚本
- `web_controller.py` - 浏览器控制界面
- `test_connection.py` - 连接测试脚本
- `android_tools/` - ADB工具目录
- `test_screenshot_*.png` - 测试截图文件

## 🚀 快速开始

1. **启动浏览器界面**:
   ```bash
   python web_controller.py
   ```

2. **运行测试脚本**:
   ```bash
   python test_connection.py
   ```

3. **编写自定义脚本**:
   ```python
   import uiautomator2 as u2
   d = u2.connect('127.0.0.1:5557')
   # 您的自动化代码
   ```

## 🔍 故障排除

### 设备连接问题

```bash
# 检查设备连接
./android_tools/platform-tools/adb.exe devices

# 重启ADB服务
./android_tools/platform-tools/adb.exe kill-server
./android_tools/platform-tools/adb.exe start-server
```

### 重新初始化UIAutomator2

```bash
# 重新初始化指定设备
python -m uiautomator2 init --serial 127.0.0.1:5557
python -m uiautomator2 init --serial emulator-5556
```

### 浏览器界面无法访问

1. 确保 `web_controller.py` 正在运行
2. 检查端口8080是否被占用
3. 尝试访问 http://127.0.0.1:8080

## 📞 技术支持

如果遇到问题，请检查：

1. 设备USB调试是否开启
2. 设备是否允许USB调试权限
3. ADB驱动是否正确安装
4. 防火墙是否阻止了连接

## 🎯 下一步

现在您可以：

1. 使用浏览器界面进行手动操作
2. 编写Python脚本进行自动化测试
3. 开发复杂的自动化流程
4. 集成到您的测试框架中

祝您使用愉快！🎉
