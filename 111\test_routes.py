#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web服务器路由
"""

import requests
import json

def test_all_routes():
    """测试所有路由"""
    print("🧪 测试Web服务器路由...")
    
    base_url = "http://localhost:8080"
    
    # 测试GET路由
    get_routes = [
        "/",
        "/device_info", 
        "/screenshot",
        "/test"
    ]
    
    print("\n📡 测试GET路由:")
    for route in get_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            print(f"  {route}: {response.status_code}")
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"    JSON: {data}")
                    except:
                        print(f"    Content length: {len(response.text)}")
                else:
                    print(f"    Content length: {len(response.text)}")
        except Exception as e:
            print(f"  {route}: ❌ {e}")
    
    # 测试POST路由
    post_routes = [
        ("/click", {"x": 100, "y": 100}),
        ("/inspect", {"x": 100, "y": 84}),
        ("/key", {"key": "home"}),
        ("/text", {"text": "test"}),
        ("/swipe", {"direction": "up"})
    ]
    
    print("\n📡 测试POST路由:")
    for route, data in post_routes:
        try:
            response = requests.post(
                f"{base_url}{route}", 
                json=data, 
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            print(f"  {route}: {response.status_code}")
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"    Response: {result}")
                except:
                    print(f"    Content: {response.text[:100]}")
        except Exception as e:
            print(f"  {route}: ❌ {e}")

def test_inspect_detailed():
    """详细测试inspect路由"""
    print("\n🔍 详细测试inspect路由...")
    
    test_data = {"x": 100, "y": 84}
    
    try:
        print(f"发送数据: {test_data}")
        
        response = requests.post(
            "http://localhost:8080/inspect",
            json=test_data,
            timeout=15,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"JSON数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except Exception as e:
                print(f"JSON解析失败: {e}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_all_routes()
    test_inspect_detailed()
