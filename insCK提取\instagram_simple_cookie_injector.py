#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Instagram简单Cookie注入器
通过浏览器方式在模拟器中注入Cookie
"""

import os
import json
import subprocess
import time
from datetime import datetime

class InstagramSimpleCookieInjector:
    def __init__(self, emulator_index=1):
        """初始化简单Cookie注入器"""
        try:
            # 雷电模拟器路径
            base_paths = [
                r"G:\leidian\LDPlayer9",
                r"G:\LDPlayer\LDPlayer9",
                r"G:\LDPlayer\LDPlayer4",
                r"C:\LDPlayer\LDPlayer9",
                r"C:\LDPlayer\LDPlayer4", 
                r"D:\LDPlayer\LDPlayer9",
                r"D:\LDPlayer\LDPlayer4"
            ]
            
            # 查找雷电安装路径
            self.base_path = None
            self.adb_path = None
            
            for path in base_paths:
                adb_exe = os.path.join(path, "adb.exe")
                if os.path.exists(adb_exe):
                    self.base_path = path
                    self.adb_path = adb_exe
                    break
            
            if not self.adb_path:
                raise FileNotFoundError("未找到雷电模拟器ADB")
            
            self.emulator_index = emulator_index
            self.username = "wavtvawavtva59"
            self.user_id = "67763611410"
            self.cookie_file = "instagram_final_cookies/wavtvawavtva59_raw_cookies.json"
            
            print(f"✅ 雷电ADB初始化成功")
            print(f"📱 模拟器索引: {emulator_index}")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def run_adb_command(self, command):
        """运行ADB命令"""
        try:
            if self.emulator_index == 0:
                full_command = [self.adb_path, "shell", command]
            else:
                full_command = [self.adb_path, "-s", f"emulator-{5554 + self.emulator_index * 2}", "shell", command]
            
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            return None
    
    def load_cookies(self):
        """加载Cookie数据"""
        try:
            print("📄 加载Cookie数据...")
            
            if not os.path.exists(self.cookie_file):
                print(f"❌ Cookie文件不存在: {self.cookie_file}")
                return None
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            cookies = data.get('cookies', {})
            
            if not cookies:
                print("❌ Cookie文件中没有Cookie数据")
                return None
            
            print(f"✅ 加载了 {len(cookies)} 个Cookie")
            return cookies
            
        except Exception as e:
            print(f"❌ 加载Cookie失败: {e}")
            return None
    
    def open_browser_in_emulator(self):
        """在模拟器中打开浏览器"""
        try:
            print("🌐 在模拟器中打开浏览器...")
            
            # 尝试打开Chrome浏览器
            chrome_result = self.run_adb_command("am start -n com.android.chrome/com.google.android.apps.chrome.Main")
            
            if chrome_result is not None:
                print("✅ Chrome浏览器已打开")
                time.sleep(3)
                return "chrome"
            
            # 如果Chrome不可用，尝试默认浏览器
            browser_result = self.run_adb_command("am start -a android.intent.action.VIEW -d https://www.instagram.com")
            
            if browser_result is not None:
                print("✅ 默认浏览器已打开")
                time.sleep(3)
                return "default"
            
            print("❌ 无法打开浏览器")
            return None
            
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            return None
    
    def create_cookie_injection_script(self, cookies):
        """创建Cookie注入脚本"""
        try:
            print("📝 创建Cookie注入脚本...")
            
            # 生成JavaScript代码
            js_code = """
// Instagram Cookie注入脚本
console.log('开始注入Instagram Cookie...');

// 清除现有Cookie
document.cookie.split(";").forEach(function(c) { 
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/;domain=.instagram.com"); 
});

// 注入新Cookie
"""
            
            for name, value in cookies.items():
                js_code += f'document.cookie = "{name}={value}; domain=.instagram.com; path=/; secure; samesite=none";\n'
            
            js_code += """
console.log('Cookie注入完成！');
console.log('当前Cookie:', document.cookie);

// 跳转到Instagram
setTimeout(function() {
    window.location.href = 'https://www.instagram.com/';
}, 2000);
"""
            
            # 保存脚本文件
            script_file = "instagram_cookie_injection.js"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(js_code)
            
            print(f"✅ Cookie注入脚本已创建: {script_file}")
            return script_file
            
        except Exception as e:
            print(f"❌ 创建Cookie注入脚本失败: {e}")
            return None
    
    def create_html_injection_page(self, cookies):
        """创建HTML注入页面"""
        try:
            print("📄 创建HTML注入页面...")
            
            html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Cookie注入器</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }}
        .container {{
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }}
        .cookie-info {{
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }}
        button {{
            background: #e91e63;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }}
        button:hover {{
            background: #c2185b;
            transform: translateY(-2px);
        }}
        .status {{
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }}
        .success {{ background: rgba(76, 175, 80, 0.3); }}
        .error {{ background: rgba(244, 67, 54, 0.3); }}
        .info {{ background: rgba(33, 150, 243, 0.3); }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🍪 Instagram Cookie注入器</h1>
        <p>为用户 <strong>{self.username}</strong> 注入Cookie</p>
        
        <div class="cookie-info">
            <h3>📋 Cookie信息:</h3>
            <ul>"""
            
            for name, value in cookies.items():
                html_content += f"<li><strong>{name}:</strong> {value[:30]}...</li>"
            
            html_content += f"""
            </ul>
        </div>
        
        <div id="status" class="status info">
            点击下面的按钮开始注入Cookie
        </div>
        
        <button onclick="injectCookies()">🚀 注入Cookie</button>
        <button onclick="goToInstagram()">📱 前往Instagram</button>
        <button onclick="checkCookies()">🔍 检查Cookie</button>
        
        <div style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
            <p>⚠️ 请确保您在Instagram域名下使用此工具</p>
            <p>🔒 此工具仅用于恢复您自己的账户</p>
        </div>
    </div>

    <script>
        function updateStatus(message, type) {{
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }}
        
        function injectCookies() {{
            updateStatus('正在注入Cookie...', 'info');
            
            try {{
                // 清除现有Cookie
                document.cookie.split(";").forEach(function(c) {{ 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/;domain=.instagram.com"); 
                }});
                
                // 注入新Cookie
"""
            
            for name, value in cookies.items():
                html_content += f'                document.cookie = "{name}={value}; domain=.instagram.com; path=/; secure; samesite=none";\n'
            
            html_content += f"""
                updateStatus('✅ Cookie注入成功！', 'success');
                
                setTimeout(function() {{
                    updateStatus('🔄 3秒后自动跳转到Instagram...', 'info');
                }}, 1000);
                
                setTimeout(function() {{
                    goToInstagram();
                }}, 3000);
                
            }} catch (error) {{
                updateStatus('❌ Cookie注入失败: ' + error.message, 'error');
            }}
        }}
        
        function goToInstagram() {{
            updateStatus('🔄 正在跳转到Instagram...', 'info');
            window.location.href = 'https://www.instagram.com/';
        }}
        
        function checkCookies() {{
            const cookies = document.cookie;
            if (cookies.includes('{self.username}') || cookies.includes('{self.user_id}')) {{
                updateStatus('✅ 检测到用户Cookie！', 'success');
            }} else {{
                updateStatus('⚠️ 未检测到用户Cookie', 'error');
            }}
            
            console.log('当前Cookie:', cookies);
        }}
        
        // 页面加载时检查当前域名
        window.onload = function() {{
            if (window.location.hostname.includes('instagram.com')) {{
                updateStatus('✅ 已在Instagram域名，可以安全注入Cookie', 'success');
            }} else {{
                updateStatus('⚠️ 不在Instagram域名，请先访问instagram.com', 'error');
            }}
        }}
    </script>
</body>
</html>"""
            
            # 保存HTML文件
            html_file = "instagram_cookie_injector.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"✅ HTML注入页面已创建: {html_file}")
            return html_file
            
        except Exception as e:
            print(f"❌ 创建HTML注入页面失败: {e}")
            return None
    
    def push_file_to_emulator(self, local_file, remote_path):
        """推送文件到模拟器"""
        try:
            push_cmd = [self.adb_path]
            if self.emulator_index != 0:
                push_cmd.extend(["-s", f"emulator-{5554 + self.emulator_index * 2}"])
            push_cmd.extend(["push", local_file, remote_path])
            
            result = subprocess.run(push_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ 文件已推送: {local_file} → {remote_path}")
                return True
            else:
                print(f"❌ 推送文件失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 推送文件异常: {e}")
            return False
    
    def run_simple_injection(self):
        """运行简单的Cookie注入流程"""
        print("🍪 Instagram简单Cookie注入器")
        print("=" * 50)
        print(f"👤 目标用户: {self.username}")
        print(f"🆔 用户ID: {self.user_id}")
        
        # 1. 加载Cookie
        cookies = self.load_cookies()
        if not cookies:
            return False
        
        # 2. 创建注入页面
        html_file = self.create_html_injection_page(cookies)
        if not html_file:
            return False
        
        # 3. 推送到模拟器
        remote_path = "/sdcard/instagram_injector.html"
        if not self.push_file_to_emulator(html_file, remote_path):
            return False
        
        # 4. 在浏览器中打开
        print("🌐 在模拟器浏览器中打开注入页面...")
        
        # 打开HTML文件
        open_result = self.run_adb_command(f"am start -a android.intent.action.VIEW -d file://{remote_path}")
        
        if open_result is not None:
            print("✅ 注入页面已在浏览器中打开")
        else:
            print("❌ 无法打开注入页面")
            return False
        
        # 5. 显示使用说明
        self.show_usage_instructions()
        
        return True
    
    def show_usage_instructions(self):
        """显示使用说明"""
        print(f"\n📋 使用说明:")
        print("=" * 30)
        print("1. 📱 在模拟器中应该已经打开了Cookie注入页面")
        print("2. 🔄 如果页面没有自动打开，请手动打开浏览器")
        print("3. 📂 访问文件: /sdcard/instagram_injector.html")
        print("4. 🍪 点击 '注入Cookie' 按钮")
        print("5. ⏳ 等待3秒自动跳转到Instagram")
        print("6. ✅ 检查是否自动登录为 wavtvawavtva59")
        
        print(f"\n🔧 手动操作步骤:")
        print("1. 在模拟器中打开浏览器")
        print("2. 访问 https://www.instagram.com")
        print("3. 按F12打开开发者工具（如果支持）")
        print("4. 在控制台中运行以下代码:")
        
        cookies = self.load_cookies()
        if cookies:
            print("\n// 清除现有Cookie")
            print("document.cookie.split(';').forEach(c => document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/;domain=.instagram.com'));")
            print("\n// 注入新Cookie")
            for name, value in cookies.items():
                print(f"document.cookie = '{name}={value}; domain=.instagram.com; path=/; secure; samesite=none';")
            print("\n// 刷新页面")
            print("location.reload();")
        
        print(f"\n💡 提示:")
        print("- 如果浏览器不支持开发者工具，使用注入页面")
        print("- 确保在instagram.com域名下操作")
        print("- Cookie有效期为30天")
        print("- 如果失败，尝试重新生成Cookie")

def main():
    """主函数"""
    print("🍪 Instagram简单Cookie注入器")
    print("通过浏览器方式在模拟器中注入Cookie")
    print("=" * 50)
    
    try:
        # 获取模拟器索引
        emulator_index = input("请输入模拟器索引 (默认1): ").strip()
        emulator_index = int(emulator_index) if emulator_index.isdigit() else 1
        
        injector = InstagramSimpleCookieInjector(emulator_index)
        success = injector.run_simple_injection()
        
        if success:
            print("\n✅ Cookie注入页面已准备就绪！")
            print("请在模拟器中按照说明操作。")
        else:
            print("\n❌ Cookie注入准备失败。")
    
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
