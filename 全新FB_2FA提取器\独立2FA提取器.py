#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新独立Facebook 2FA提取器
完全重新设计，避免任何数据共享或缓存问题
每次运行都是完全独立的提取过程
"""

import os
import sys
import json
import subprocess
import hashlib
import base64
import binascii
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

class IndependentFB2FAExtractor:
    def __init__(self):
        # 创建完全独立的工作目录
        self.session_id = hashlib.md5(str(datetime.now()).encode()).hexdigest()[:8]
        self.work_dir = Path(f"temp_2fa_session_{self.session_id}")
        self.work_dir.mkdir(exist_ok=True)
        
        # ADB配置
        self.adb_path = r"G:\leidian\LDPlayer9\adb.exe"
        
        # 结果存储
        self.results = {
            'session_id': self.session_id,
            'timestamp': datetime.now().isoformat(),
            'devices': {},
            'extraction_log': []
        }
        
        print(f"🆕 创建独立会话: {self.session_id}")
        print(f"📁 工作目录: {self.work_dir}")
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        self.results['extraction_log'].append(log_entry)
    
    def execute_adb_command(self, device_id, command, timeout=60):
        """执行ADB命令 - 完全独立的执行"""
        try:
            # 创建临时脚本文件避免命令缓存
            temp_script = self.work_dir / f"cmd_{hashlib.md5(command.encode()).hexdigest()[:8]}.sh"
            
            full_command = f'"{self.adb_path}" -s {device_id} shell "su -c \'{command}\'"'
            
            with open(temp_script, 'w') as f:
                f.write(full_command)
            
            self.log(f"执行命令: {command[:50]}...")
            
            result = subprocess.run(
                full_command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                cwd=str(self.work_dir)
            )
            
            # 清理临时文件
            if temp_script.exists():
                temp_script.unlink()
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                self.log(f"命令执行失败: {result.stderr}")
                return None
                
        except Exception as e:
            self.log(f"命令执行异常: {e}")
            return None
    
    def discover_devices(self):
        """重新发现所有设备"""
        self.log("🔍 重新发现设备...")
        
        # 重启ADB服务确保干净状态
        subprocess.run(f'"{self.adb_path}" kill-server', shell=True, capture_output=True)
        subprocess.run(f'"{self.adb_path}" start-server', shell=True, capture_output=True)
        
        # 获取设备列表
        result = subprocess.run(
            f'"{self.adb_path}" devices', 
            shell=True, 
            capture_output=True, 
            text=True
        )
        
        devices = []
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            for line in lines:
                if line.strip() and 'device' in line:
                    device_id = line.split()[0]
                    devices.append(device_id)
                    self.log(f"发现设备: {device_id}")
        
        return devices
    
    def get_device_info(self, device_id):
        """获取设备详细信息"""
        self.log(f"📱 分析设备: {device_id}")
        
        device_info = {
            'device_id': device_id,
            'serial': None,
            'model': None,
            'facebook_installed': False,
            'facebook_user_id': None,
            'facebook_accessible': False,
            'totp_keys': [],
            'raw_data': {}
        }
        
        # 获取设备基本信息
        serial = self.execute_adb_command(device_id, "getprop ro.serialno")
        model = self.execute_adb_command(device_id, "getprop ro.product.model")
        
        device_info['serial'] = serial
        device_info['model'] = model
        
        self.log(f"  序列号: {serial}")
        self.log(f"  型号: {model}")
        
        # 检查Facebook安装
        fb_check = self.execute_adb_command(device_id, "pm list packages | grep facebook")
        if fb_check and "com.facebook.katana" in fb_check:
            device_info['facebook_installed'] = True
            self.log("  ✅ Facebook已安装")
            
            # 检查数据访问权限
            data_check = self.execute_adb_command(device_id, "ls /data/data/com.facebook.katana/")
            if data_check:
                device_info['facebook_accessible'] = True
                self.log("  ✅ Facebook数据可访问")
                
                # 获取用户ID
                user_id = self.get_facebook_user_id(device_id)
                device_info['facebook_user_id'] = user_id
                
                if user_id:
                    self.log(f"  👤 Facebook用户ID: {user_id}")
                else:
                    self.log("  ❌ 无法获取用户ID")
            else:
                self.log("  ❌ Facebook数据不可访问")
        else:
            self.log("  ❌ Facebook未安装")
        
        return device_info
    
    def get_facebook_user_id(self, device_id):
        """获取Facebook用户ID - 多种方法"""
        methods = [
            # 方法1: 从msys-auth-data.xml
            "cat /data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml | grep -o '[0-9]\\{10,15\\}' | head -1",
            
            # 方法2: 从prefs_db数据库
            "strings /data/data/com.facebook.katana/databases/prefs_db | grep -o 'logged_in_user_scoped/[0-9]\\{10,15\\}' | head -1 | cut -d'/' -f2",
            
            # 方法3: 从其他配置文件
            "find /data/data/com.facebook.katana/shared_prefs/ -name '*.xml' -exec grep -o '[0-9]\\{10,15\\}' {} \\; | head -1"
        ]
        
        for i, method in enumerate(methods, 1):
            self.log(f"    尝试方法{i}获取用户ID...")
            result = self.execute_adb_command(device_id, method)
            if result and result.isdigit() and len(result) >= 10:
                self.log(f"    ✅ 方法{i}成功: {result}")
                return result
            else:
                self.log(f"    ❌ 方法{i}失败")
        
        return None
    
    def extract_totp_keys_independent(self, device_id, user_id):
        """独立提取TOTP密钥 - 避免任何缓存"""
        self.log(f"🔑 独立提取TOTP密钥 (设备: {device_id}, 用户: {user_id})")
        
        # 确保Facebook应用处于活跃状态
        self.execute_adb_command(device_id, "am start -n com.facebook.katana/.LoginActivity")
        
        # 等待应用启动
        import time
        time.sleep(3)
        
        found_keys = []
        
        # 创建设备专用的临时目录
        device_temp_dir = self.work_dir / f"device_{device_id.replace(':', '_').replace('-', '_')}"
        device_temp_dir.mkdir(exist_ok=True)
        
        # 方法1: 直接从prefs_db提取十六进制
        self.log("  🔍 方法1: 从prefs_db提取十六进制...")
        hex_keys = self.extract_hex_from_prefs_db(device_id, device_temp_dir)
        
        for hex_key in hex_keys:
            base32_key = self.hex_to_base32_independent(hex_key)
            if base32_key and len(base32_key) >= 16:
                key_info = {
                    'type': 'HEX_TO_BASE32',
                    'original_hex': hex_key,
                    'base32_key': base32_key,
                    'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                    'length': len(base32_key),
                    'extraction_method': 'prefs_db_hex',
                    'device_id': device_id,
                    'user_id': user_id
                }
                found_keys.append(key_info)
                self.log(f"    ✅ 十六进制转换: {hex_key} → {base32_key}")
        
        # 方法2: 直接搜索Base32格式
        self.log("  🔍 方法2: 直接搜索Base32格式...")
        base32_keys = self.extract_base32_direct(device_id, device_temp_dir)
        
        for base32_key in base32_keys:
            if self.validate_totp_key_independent(base32_key):
                key_info = {
                    'type': 'DIRECT_BASE32',
                    'base32_key': base32_key,
                    'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                    'length': len(base32_key),
                    'extraction_method': 'direct_base32',
                    'device_id': device_id,
                    'user_id': user_id
                }
                found_keys.append(key_info)
                self.log(f"    ✅ 直接Base32: {base32_key}")
        
        # 方法3: 从内存转储提取
        self.log("  🔍 方法3: 从内存转储提取...")
        memory_keys = self.extract_from_memory_dump(device_id, device_temp_dir)
        
        for key in memory_keys:
            key['device_id'] = device_id
            key['user_id'] = user_id
            found_keys.append(key)
            self.log(f"    ✅ 内存提取: {key['base32_key']}")
        
        # 去重
        unique_keys = self.deduplicate_keys(found_keys)
        
        self.log(f"  📊 总共找到 {len(found_keys)} 个密钥，去重后 {len(unique_keys)} 个")
        
        return unique_keys
    
    def extract_hex_from_prefs_db(self, device_id, temp_dir):
        """从prefs_db提取十六进制密钥"""
        # 创建数据库的独立副本
        db_copy = temp_dir / "prefs_db_copy"
        
        # 复制数据库文件
        copy_cmd = f"cp /data/data/com.facebook.katana/databases/prefs_db {db_copy}"
        self.execute_adb_command(device_id, copy_cmd)
        
        # 从副本中提取数据
        extract_cmd = f"strings {db_copy} | grep -o '[a-fA-F0-9]\\{{40,52\\}}'"
        result = self.execute_adb_command(device_id, extract_cmd)
        
        hex_keys = []
        if result:
            for line in result.split('\n'):
                if line.strip() and len(line.strip()) in [40, 52]:
                    hex_keys.append(line.strip())
        
        # 清理副本
        self.execute_adb_command(device_id, f"rm -f {db_copy}")
        
        return list(set(hex_keys))  # 去重
    
    def extract_base32_direct(self, device_id, temp_dir):
        """直接搜索Base32格式密钥"""
        search_commands = [
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{16\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{20\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null",
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{32\\}' {} \\; 2>/dev/null"
        ]
        
        base32_keys = []
        for cmd in search_commands:
            result = self.execute_adb_command(device_id, cmd)
            if result:
                for line in result.split('\n'):
                    if line.strip() and len(line.strip()) >= 16:
                        base32_keys.append(line.strip())
        
        return list(set(base32_keys))  # 去重
    
    def extract_from_memory_dump(self, device_id, temp_dir):
        """从内存转储提取密钥"""
        memory_keys = []
        
        # 获取Facebook进程ID
        ps_result = self.execute_adb_command(device_id, "ps | grep facebook")
        if not ps_result:
            return memory_keys
        
        try:
            # 尝试从进程内存中提取数据
            pid = ps_result.split()[1] if ps_result.split() else None
            if pid and pid.isdigit():
                # 读取进程的maps文件
                maps_cmd = f"cat /proc/{pid}/maps | grep -E 'heap|stack'"
                maps_result = self.execute_adb_command(device_id, maps_cmd)
                
                if maps_result:
                    self.log(f"    找到进程 {pid} 的内存映射")
                    # 这里可以添加更复杂的内存分析
                    # 由于安全限制，简化处理
        except:
            pass
        
        return memory_keys
    
    def hex_to_base32_independent(self, hex_string):
        """独立的十六进制转Base32转换"""
        try:
            # 清理输入
            hex_clean = hex_string.replace(' ', '').replace('\n', '').replace('\r', '')
            
            # 验证十六进制格式
            if not all(c in '0123456789abcdefABCDEF' for c in hex_clean):
                return None
            
            # 确保长度为偶数
            if len(hex_clean) % 2 != 0:
                return None
            
            # 转换为字节
            bytes_data = bytes.fromhex(hex_clean)
            
            # 转换为Base32
            base32_string = base64.b32encode(bytes_data).decode('ascii')
            
            # 移除填充
            base32_clean = base32_string.rstrip('=')
            
            return base32_clean
            
        except Exception as e:
            return None
    
    def validate_totp_key_independent(self, key):
        """独立验证TOTP密钥"""
        try:
            # 长度检查
            if len(key) < 16 or len(key) > 32:
                return False
            
            # 字符检查
            valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
            if not set(key.upper()).issubset(valid_chars):
                return False
            
            # Base32解码测试
            padding_needed = (8 - len(key) % 8) % 8
            padded_key = key + '=' * padding_needed
            decoded = base64.b32decode(padded_key)
            
            # 解码后长度检查
            if len(decoded) < 10 or len(decoded) > 64:
                return False
            
            # 排除明显的非密钥字符串
            exclude_patterns = [
                'FACEBOOK', 'GOOGLE', 'ANDROID', 'SYSTEM', 'AAAAAAA', 'BBBBBBB',
                'CCCCCCC', 'DDDDDDD', 'EEEEEEE', 'FFFFFFF', 'GGGGGGG'
            ]
            
            for pattern in exclude_patterns:
                if pattern in key.upper():
                    return False
            
            return True
            
        except:
            return False
    
    def deduplicate_keys(self, keys):
        """去重密钥"""
        seen = set()
        unique_keys = []
        
        for key in keys:
            key_value = key['base32_key']
            if key_value not in seen:
                seen.add(key_value)
                unique_keys.append(key)
        
        return unique_keys
    
    def save_results(self):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"独立2FA提取结果_{self.session_id}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def cleanup(self):
        """清理临时文件"""
        try:
            if self.work_dir.exists():
                shutil.rmtree(self.work_dir)
                self.log(f"🧹 清理工作目录: {self.work_dir}")
        except:
            pass
    
    def run_independent_extraction(self):
        """运行独立提取流程"""
        try:
            self.log("🚀 开始独立2FA提取流程")
            self.log("=" * 60)
            
            # 1. 发现设备
            devices = self.discover_devices()
            
            if not devices:
                self.log("❌ 未发现任何设备")
                return False
            
            self.log(f"📱 发现 {len(devices)} 个设备")
            
            # 2. 分析每个设备
            for device_id in devices:
                self.log(f"\n🔍 分析设备: {device_id}")
                
                device_info = self.get_device_info(device_id)
                self.results['devices'][device_id] = device_info
                
                # 3. 如果设备有Facebook且可访问，提取TOTP密钥
                if device_info['facebook_accessible'] and device_info['facebook_user_id']:
                    totp_keys = self.extract_totp_keys_independent(
                        device_id, 
                        device_info['facebook_user_id']
                    )
                    device_info['totp_keys'] = totp_keys
                    
                    if totp_keys:
                        self.log(f"✅ 设备 {device_id} 提取到 {len(totp_keys)} 个TOTP密钥")
                    else:
                        self.log(f"❌ 设备 {device_id} 未找到TOTP密钥")
                else:
                    self.log(f"⚠️ 设备 {device_id} 跳过TOTP提取")
            
            # 4. 显示结果
            self.display_results()
            
            # 5. 保存结果
            filename = self.save_results()
            self.log(f"💾 结果已保存到: {filename}")
            
            return True
            
        except Exception as e:
            self.log(f"❌ 提取过程出错: {e}")
            return False
        
        finally:
            # 6. 清理
            self.cleanup()
    
    def display_results(self):
        """显示提取结果"""
        self.log("\n" + "=" * 60)
        self.log("📊 独立2FA提取结果")
        self.log("=" * 60)
        
        for device_id, device_info in self.results['devices'].items():
            self.log(f"\n📱 设备: {device_id}")
            self.log(f"   序列号: {device_info['serial']}")
            self.log(f"   型号: {device_info['model']}")
            self.log(f"   Facebook用户ID: {device_info['facebook_user_id']}")
            
            if device_info['totp_keys']:
                self.log(f"   🔑 TOTP密钥 ({len(device_info['totp_keys'])}个):")
                
                for i, key in enumerate(device_info['totp_keys'], 1):
                    self.log(f"      {i}. {key['base32_key']}")
                    self.log(f"         格式化: {key['formatted']}")
                    self.log(f"         长度: {key['length']} 字符")
                    self.log(f"         提取方法: {key['extraction_method']}")
                    if 'original_hex' in key:
                        self.log(f"         原始十六进制: {key['original_hex']}")
            else:
                self.log("   ❌ 未找到TOTP密钥")

def main():
    """主函数"""
    print("🆕 全新独立Facebook 2FA提取器")
    print("完全重新设计，避免任何数据共享问题")
    print("=" * 60)
    
    extractor = IndependentFB2FAExtractor()
    
    try:
        success = extractor.run_independent_extraction()
        
        if success:
            print("\n✅ 独立提取完成！")
            print("💡 每个设备的TOTP密钥都是独立提取的，确保无数据共享")
        else:
            print("\n❌ 提取失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        extractor.cleanup()
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        extractor.cleanup()

if __name__ == "__main__":
    main()
