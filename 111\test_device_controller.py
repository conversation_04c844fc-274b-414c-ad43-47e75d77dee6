#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeviceController类
"""

import sys
sys.path.append('.')

from web_controller import DeviceController

def test_device_controller():
    """测试DeviceController类"""
    print("🧪 测试DeviceController类...")
    
    # 创建控制器实例
    controller = DeviceController()
    print(f"✅ 控制器创建成功")
    
    # 检查设备连接
    devices = list(controller.devices.keys())
    print(f"📱 发现设备: {devices}")

    if not devices:
        print("❌ 没有发现设备")
        return

    print(f"🎯 当前设备: {controller.current_device}")

    if not controller.current_device:
        print("❌ 没有当前设备")
        return
    
    # 测试元素检查功能
    test_coordinates = [
        (100, 84),   # v2rayNG应用图标
        (159, 84),   # Instagram应用图标
        (41, 137),   # MT管理器应用图标
        (50, 50),    # 通用测试点
    ]
    
    for x, y in test_coordinates:
        print(f"\n🎯 测试坐标 ({x}, {y}):")
        
        try:
            element_info = controller.get_element_at_position(x, y)
            
            if element_info:
                print(f"✅ 找到元素:")
                for key, value in element_info.items():
                    if value:
                        print(f"   {key}: {value}")
            else:
                print(f"❌ 未找到元素")
                
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_device_controller()
