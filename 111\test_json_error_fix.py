#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析错误修复
"""

import requests
import json
import time

def test_json_error_scenarios():
    """测试各种可能导致JSON解析错误的场景"""
    print("🧪 测试JSON解析错误修复...")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试正常的API调用
    print("\n1. 测试正常API调用:")
    test_cases = [
        ("GET", "/device_info", None),
        ("GET", "/screenshot", None),
        ("POST", "/inspect", {"x": 100, "y": 100}),
        ("POST", "/click", {"x": 50, "y": 50}),
        ("POST", "/key", {"key": "home"}),
        ("POST", "/swipe", {"direction": "up"})
    ]
    
    for method, endpoint, data in test_cases:
        try:
            print(f"\n📡 测试 {method} {endpoint}")
            
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{endpoint}", 
                    json=data, 
                    timeout=5,
                    headers={'Content-Type': 'application/json'}
                )
            
            print(f"   状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
            
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    print(f"   ✅ JSON解析成功: {json_data.get('success', 'N/A')}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   响应内容前100字符: {response.text[:100]}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应内容前100字符: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求异常: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")
    
    # 测试错误场景
    print("\n\n2. 测试错误场景:")
    error_cases = [
        ("GET", "/nonexistent", None),  # 404错误
        ("POST", "/inspect", {"invalid": "data"}),  # 无效数据
        ("POST", "/click", {}),  # 缺少必需参数
    ]
    
    for method, endpoint, data in error_cases:
        try:
            print(f"\n📡 测试错误场景 {method} {endpoint}")
            
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{endpoint}", 
                    json=data, 
                    timeout=5,
                    headers={'Content-Type': 'application/json'}
                )
            
            print(f"   状态码: {response.status_code}")
            print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
            
            # 检查是否返回HTML（可能导致JSON解析错误）
            content_type = response.headers.get('content-type', '')
            if 'html' in content_type.lower():
                print(f"   ⚠️  返回HTML内容，可能导致JSON解析错误")
                print(f"   响应内容前200字符: {response.text[:200]}")
            elif 'json' in content_type.lower():
                try:
                    json_data = response.json()
                    print(f"   ✅ JSON解析成功: {json_data}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
            else:
                print(f"   响应内容前100字符: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求异常: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")

def test_browser_simulation():
    """模拟浏览器行为测试"""
    print("\n\n3. 模拟浏览器JavaScript行为:")
    
    # 模拟浏览器的fetch请求
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    test_data = {"x": 130, "y": 200}
    
    try:
        print(f"\n📱 模拟浏览器inspect请求:")
        print(f"   发送数据: {test_data}")
        
        response = requests.post(
            "http://localhost:8080/inspect",
            json=test_data,
            headers=headers,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        # 检查Content-Type
        content_type = response.headers.get('content-type', '')
        print(f"   Content-Type: {content_type}")
        
        if response.status_code == 200:
            if 'application/json' in content_type:
                try:
                    data = response.json()
                    print(f"   ✅ JSON解析成功")
                    if data.get('success') and data.get('element'):
                        element = data['element']
                        print(f"   找到元素: {element.get('class', '')} - {element.get('text', '')}")
                    else:
                        print(f"   未找到元素: {data.get('error', '未知错误')}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   响应内容: {response.text[:500]}")
            else:
                print(f"   ⚠️  非JSON响应，可能导致前端错误")
                print(f"   响应内容前200字符: {response.text[:200]}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")

def main():
    """主函数"""
    print("🚀 开始JSON错误修复测试...")
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 运行测试
    test_json_error_scenarios()
    test_browser_simulation()
    
    print("\n" + "=" * 60)
    print("🎉 JSON错误修复测试完成！")
    
    print("\n📋 修复说明:")
    print("1. 添加了响应状态码检查")
    print("2. 添加了Content-Type验证")
    print("3. 改进了错误处理和日志记录")
    print("4. 现在浏览器控制台会显示详细的错误信息")
    print("5. 如果仍有问题，请检查浏览器开发者工具的控制台")

if __name__ == "__main__":
    main()
