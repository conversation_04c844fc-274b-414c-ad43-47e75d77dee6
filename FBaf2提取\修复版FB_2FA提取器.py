#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Facebook 2FA提取器
解决权限问题的Facebook双因素认证数据提取工具
"""

import os
import sys
import json
import re
import base64
from datetime import datetime

# 添加雷电API路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from 雷电API import Dnconsole

class FixedFacebook2FAExtractor:
    def __init__(self, emulator_index=11):
        """初始化修复版Facebook 2FA提取器"""
        try:
            # 雷电模拟器路径配置
            base_paths = [
                r"G:\leidian\LDPlayer9",
                r"G:\LDPlayer\LDPlayer9", 
                r"C:\LDPlayer\LDPlayer9",
                r"D:\LDPlayer\LDPlayer9",
                r"E:\LDPlayer\LDPlayer9"
            ]
            
            self.base_path = None
            for path in base_paths:
                if os.path.exists(os.path.join(path, "ld.exe")):
                    self.base_path = path
                    break
            
            if not self.base_path:
                raise FileNotFoundError("未找到雷电模拟器安装路径")
            
            self.share_path = os.path.expanduser("~/Documents/leidian64")
            self.emulator_index = emulator_index
            
            # 初始化雷电控制台
            self.ld = Dnconsole(
                base_path=self.base_path,
                share_path=self.share_path,
                emulator_id=emulator_index
            )
            
            print(f"🔧 修复版Facebook 2FA提取器初始化成功")
            print(f"📱 目标模拟器: {emulator_index}")
            
            # 初始化提取结果
            self.extraction_results = {
                'totp_keys': [],
                'backup_codes': [],
                'user_info': {},
                'raw_data': [],
                'file_contents': {}
            }
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def execute_command_with_retry(self, command):
        """执行命令并重试多种方式"""
        try:
            # 方式1: 直接执行
            success, output = self.ld.execute_ld(self.emulator_index, command)
            if success and output and "Permission denied" not in output:
                return output
            
            # 方式2: 使用su -c
            su_command = f'su -c "{command}"'
            success, output = self.ld.execute_ld(self.emulator_index, su_command)
            if success and output and "Permission denied" not in output:
                return output
            
            # 方式3: 使用su root -c
            root_command = f'su root -c "{command}"'
            success, output = self.ld.execute_ld(self.emulator_index, root_command)
            if success and output and "Permission denied" not in output:
                return output
            
            # 方式4: 修改权限后再读取
            chmod_command = f"chmod 755 {command.split()[-1]} 2>/dev/null; {command}"
            success, output = self.ld.execute_ld(self.emulator_index, chmod_command)
            if success and output and "Permission denied" not in output:
                return output
            
            return None
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return None
    
    def test_facebook_access(self):
        """测试Facebook目录访问"""
        print("\n🔍 测试Facebook目录访问...")
        print("=" * 50)
        
        # 测试基本目录访问
        test_commands = [
            "ls -la /data/data/com.facebook.katana/",
            "find /data/data/com.facebook.katana/ -maxdepth 1 -type d",
            "ls -la /data/data/com.facebook.katana/shared_prefs/",
            "ls -la /data/data/com.facebook.katana/files/",
        ]
        
        for cmd in test_commands:
            print(f"\n测试命令: {cmd}")
            result = self.execute_command_with_retry(cmd)
            if result:
                print(f"✅ 成功: {result[:200]}...")
                return True
            else:
                print(f"❌ 失败")
        
        return False
    
    def extract_facebook_files_directly(self):
        """直接提取Facebook文件内容"""
        print("\n📄 直接提取Facebook文件...")
        print("=" * 50)
        
        found_data = []
        
        # 重要的Facebook文件路径
        important_files = [
            "/data/data/com.facebook.katana/shared_prefs/msys-auth-data.xml",
            "/data/data/com.facebook.katana/shared_prefs/com.facebook.secure.switchoff.xml",
            "/data/data/com.facebook.katana/shared_prefs/authentication.xml",
            "/data/data/com.facebook.katana/files/auth_data",
            "/data/data/com.facebook.katana/files/user_data",
        ]
        
        # 尝试读取每个重要文件
        for file_path in important_files:
            print(f"\n📄 尝试读取: {os.path.basename(file_path)}")
            
            # 多种读取方式
            read_commands = [
                f"cat {file_path}",
                f"cat '{file_path}'",
                f"head -100 {file_path}",
                f"strings {file_path}",
            ]
            
            for read_cmd in read_commands:
                content = self.execute_command_with_retry(read_cmd)
                if content and len(content) > 10:
                    print(f"   ✅ 成功读取 ({len(content)} 字符)")
                    
                    # 保存文件内容
                    self.extraction_results['file_contents'][file_path] = content
                    
                    # 搜索2FA数据
                    found_2fa = self.search_all_2fa_patterns(content, os.path.basename(file_path))
                    found_data.extend(found_2fa)
                    
                    break
            else:
                print(f"   ❌ 无法读取")
        
        return found_data
    
    def extract_using_find_and_grep(self):
        """使用find和grep搜索2FA数据"""
        print("\n🔍 使用find和grep搜索...")
        print("=" * 50)
        
        found_data = []
        
        # 搜索包含2FA相关关键词的文件
        search_patterns = [
            "totp",
            "backup",
            "recovery",
            "secret",
            "auth",
            "2fa",
            "two_factor",
            "authenticator"
        ]
        
        for pattern in search_patterns:
            print(f"\n🔍 搜索关键词: {pattern}")
            
            # 在Facebook目录中搜索
            search_cmd = f"find /data/data/com.facebook.katana/ -type f -exec grep -l '{pattern}' {{}} \\; 2>/dev/null"
            result = self.execute_command_with_retry(search_cmd)
            
            if result:
                files = [f.strip() for f in result.split('\n') if f.strip()]
                print(f"   找到 {len(files)} 个包含'{pattern}'的文件")
                
                for file_path in files[:5]:  # 限制文件数量
                    print(f"      📄 {file_path}")
                    
                    # 读取文件内容
                    content = self.execute_command_with_retry(f"cat '{file_path}'")
                    if content:
                        self.extraction_results['file_contents'][file_path] = content
                        
                        # 搜索2FA数据
                        found_2fa = self.search_all_2fa_patterns(content, os.path.basename(file_path))
                        found_data.extend(found_2fa)
        
        return found_data
    
    def search_all_2fa_patterns(self, content, source_file):
        """搜索所有2FA模式"""
        found_data = []
        
        if not content:
            return found_data
        
        # 标准Base32 TOTP密钥模式 (重点搜索26字符)
        totp_patterns = [
            # 最重要: 26字符标准TOTP密钥 (如 Z34OJGIQ5SOTPXV2SR2AZI3IXM)
            (r'\b[A-Z2-7]{26}\b', 'TOTP_26_STANDARD', '⭐ 26字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{32}\b', 'TOTP_32_STANDARD', '32字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{20}\b', 'TOTP_20_STANDARD', '20字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{24}\b', 'TOTP_24_STANDARD', '24字符标准TOTP密钥'),
            (r'\b[A-Z2-7]{16}\b', 'TOTP_16_STANDARD', '16字符标准TOTP密钥'),

            # 搜索可能的变体格式
            (r'[A-Z2-7]{4}[A-Z2-7]{4}[A-Z2-7]{4}[A-Z2-7]{4}[A-Z2-7]{4}[A-Z2-7]{4}[A-Z2-7]{2}', 'TOTP_26_SPACED', '26字符分组TOTP密钥'),
        ]
        
        # 备份代码模式
        backup_patterns = [
            (r'\b[A-Z0-9]{8}\b', 'BACKUP_8', '8字符备份代码'),
            (r'\b[A-Z0-9]{10}\b', 'BACKUP_10', '10字符备份代码'),
            (r'\b[A-Z0-9]{12}\b', 'BACKUP_12', '12字符备份代码'),
        ]
        
        # 用户信息模式
        user_patterns = [
            (r'"user_id"[:\s]*"?(\d+)"?', 'USER_ID', '用户ID'),
            (r'"uid"[:\s]*"?(\d+)"?', 'USER_ID', '用户ID'),
            (r'"email"[:\s]*"([^"]+@[^"]+)"', 'EMAIL', '邮箱'),
            (r'"phone"[:\s]*"(\+?\d{10,15})"', 'PHONE', '手机号'),
        ]
        
        all_patterns = totp_patterns + backup_patterns + user_patterns
        
        for pattern, data_type, description in all_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # 验证数据有效性
                if self.validate_2fa_data(match, data_type):
                    data_info = {
                        'type': data_type,
                        'value': match,
                        'length': len(match),
                        'description': description,
                        'source': source_file,
                        'context': self.get_context(content, match)
                    }
                    
                    found_data.append(data_info)
                    
                    print(f"      🔑 发现: {description}")
                    print(f"         值: {match}")
                    print(f"         来源: {source_file}")
        
        return found_data
    
    def validate_2fa_data(self, data, data_type):
        """验证2FA数据有效性"""
        try:
            if data_type.startswith('TOTP_'):
                # 验证Base32 TOTP密钥
                valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567')
                if not set(data.upper()).issubset(valid_chars):
                    return False
                
                # 排除明显的英文单词和常见模式
                exclude_words = [
                    'FACEBOOK', 'GOOGLE', 'TWITTER', 'INSTAGRAM', 'ANDROID', 'SYSTEM',
                    'AMBIENT', 'DIFFUSE', 'STATIC', 'ENVIRONMENT', 'TEXTURE', 'ROTATION',
                    'FACTOR', 'SHADING', 'PARAMS', 'FAVORITE', 'MESSENGER', 'CONTACT',
                    'ENCODING', 'REDIRECT', 'STANDALONE', 'DIALTONE', 'FBINTERNAL'
                ]

                data_upper = data.upper()
                for word in exclude_words:
                    if word in data_upper:
                        return False

                # 检查是否包含过多重复字符
                char_counts = {}
                for char in data:
                    char_counts[char] = char_counts.get(char, 0) + 1

                # 如果任何字符出现超过总长度的40%，可能不是真正的密钥
                max_char_ratio = max(char_counts.values()) / len(data)
                if max_char_ratio > 0.4:
                    return False
                
                # 尝试Base32解码
                padded_data = data + '=' * (8 - len(data) % 8) % 8
                decoded = base64.b32decode(padded_data)
                
                # 检查解码后的长度
                if len(decoded) < 10 or len(decoded) > 64:
                    return False
                
                return True
                
            elif data_type.startswith('BACKUP_'):
                # 验证备份代码
                if len(data) < 6 or len(data) > 15:
                    return False
                
                # 排除明显不是备份代码的内容
                if data.isdigit() and (data.startswith('0000') or data.startswith('1111')):
                    return False
                
                return True
                
            elif data_type in ['USER_ID', 'EMAIL', 'PHONE']:
                # 用户信息验证
                return len(data) > 3
                
            return True
            
        except Exception:
            return False
    
    def get_context(self, content, target):
        """获取上下文"""
        try:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if target in line:
                    start = max(0, i - 2)
                    end = min(len(lines), i + 3)
                    context_lines = lines[start:end]
                    return '\n'.join(context_lines)
            return target
        except:
            return target

    def search_26_char_totp_specifically(self):
        """专门搜索26字符TOTP密钥 - 基于成功经验"""
        print("\n🎯 专门搜索26字符TOTP密钥...")
        print("=" * 50)
        print("🎯 目标: 寻找类似 Z34OJGIQ5SOTPXV2SR2AZI3IXM 的26字符密钥")
        print("📋 基于之前成功找到密钥的经验进行深度搜索")

        found_data = []

        # 1. 搜索所有可能的编码格式
        encoding_searches = [
            # 直接搜索26字符Base32
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Z2-7]\\{26\\}' {} \\; 2>/dev/null",

            # 搜索可能的十六进制编码
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[a-fA-F0-9]\\{52\\}' {} \\; 2>/dev/null",

            # 搜索Base64编码 (26字符Base32对应的Base64长度)
            "find /data/data/com.facebook.katana/ -type f -exec grep -o '[A-Za-z0-9+/]\\{35,40\\}={0,2}' {} \\; 2>/dev/null",
        ]

        for cmd in encoding_searches:
            print(f"\n🔍 搜索编码格式...")
            result = self.execute_command_with_retry(cmd)
            if result:
                print(f"   📊 找到编码数据 ({len(result)} 字符)")
                # 尝试解码和转换
                decoded_keys = self.try_decode_to_totp(result, "编码搜索")
                found_data.extend(decoded_keys)

        # 2. 深度搜索数据库文件
        db_data = self.search_database_files_deeply()
        found_data.extend(db_data)

        # 3. 搜索特定的Facebook认证文件
        auth_data = self.search_facebook_auth_files()
        found_data.extend(auth_data)

        # 4. 搜索可能的分片或分段存储
        fragment_data = self.search_fragmented_keys()
        found_data.extend(fragment_data)

        return found_data

    def try_decode_to_totp(self, encoded_data, source):
        """尝试将编码数据解码为TOTP密钥"""
        found_keys = []

        if not encoded_data:
            return found_keys

        lines = encoded_data.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 尝试十六进制解码
            if len(line) == 52 and all(c in '0123456789abcdefABCDEF' for c in line):
                try:
                    import binascii
                    decoded_bytes = binascii.unhexlify(line)
                    # 尝试转换为Base32
                    import base64
                    base32_key = base64.b32encode(decoded_bytes).decode().rstrip('=')
                    if len(base32_key) == 26:
                        print(f"      🔓 十六进制解码成功: {base32_key}")
                        if self.is_genuine_totp_key(base32_key):
                            found_keys.append({
                                'type': 'TOTP_26_DECODED',
                                'value': base32_key,
                                'length': 26,
                                'description': '⭐ 解码得到的26字符TOTP密钥',
                                'source': f"{source}(hex解码)",
                                'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                                'original_hex': line
                            })
                except:
                    continue

            # 尝试Base64解码
            if len(line) >= 35 and len(line) <= 40:
                try:
                    import base64
                    decoded_bytes = base64.b64decode(line + '==')
                    # 尝试转换为Base32
                    base32_key = base64.b32encode(decoded_bytes).decode().rstrip('=')
                    if len(base32_key) == 26:
                        print(f"      🔓 Base64解码成功: {base32_key}")
                        if self.is_genuine_totp_key(base32_key):
                            found_keys.append({
                                'type': 'TOTP_26_DECODED',
                                'value': base32_key,
                                'length': 26,
                                'description': '⭐ 解码得到的26字符TOTP密钥',
                                'source': f"{source}(base64解码)",
                                'formatted': ' '.join([base32_key[i:i+4] for i in range(0, len(base32_key), 4)]),
                                'original_base64': line
                            })
                except:
                    continue

        return found_keys

    def search_database_files_deeply(self):
        """深度搜索数据库文件"""
        print(f"\n🗄️ 深度搜索数据库文件...")
        found_data = []

        # 查找所有数据库文件
        db_search = self.execute_command_with_retry("find /data/data/com.facebook.katana/ -name '*.db' -o -name '*.sqlite' -o -name '*.sqlite3' 2>/dev/null")

        if db_search:
            db_files = [f.strip() for f in db_search.split('\n') if f.strip()]
            print(f"   📊 发现 {len(db_files)} 个数据库文件")

            for db_file in db_files:
                print(f"      🗄️ 分析: {os.path.basename(db_file)}")

                # 使用strings命令提取可读字符串
                strings_result = self.execute_command_with_retry(f"strings '{db_file}' 2>/dev/null")
                if strings_result:
                    # 搜索TOTP相关数据
                    totp_keys = self.extract_26_char_totp_from_text(strings_result, f"数据库:{os.path.basename(db_file)}")
                    found_data.extend(totp_keys)

                    # 搜索可能的编码数据
                    decoded_keys = self.try_decode_to_totp(strings_result, f"数据库:{os.path.basename(db_file)}")
                    found_data.extend(decoded_keys)

        return found_data

    def search_facebook_auth_files(self):
        """搜索Facebook认证相关文件"""
        print(f"\n🔐 搜索Facebook认证文件...")
        found_data = []

        # 重点搜索的认证文件
        auth_files = [
            "msys-auth-data.xml",
            "com.facebook.secure.switchoff.xml",
            "authentication.xml",
            "auth_data",
            "user_data",
            "session_data",
            "token_data",
            "credential_data"
        ]

        for auth_file in auth_files:
            search_result = self.execute_command_with_retry(f"find /data/data/com.facebook.katana/ -name '{auth_file}' 2>/dev/null")

            if search_result:
                files = [f.strip() for f in search_result.split('\n') if f.strip()]
                for file_path in files:
                    print(f"      🔐 深度分析: {auth_file}")

                    # 读取文件内容
                    content = self.execute_command_with_retry(f"cat '{file_path}' 2>/dev/null")
                    if content:
                        # 搜索TOTP密钥
                        totp_keys = self.extract_26_char_totp_from_text(content, f"认证文件:{auth_file}")
                        found_data.extend(totp_keys)

                        # 搜索编码数据
                        decoded_keys = self.try_decode_to_totp(content, f"认证文件:{auth_file}")
                        found_data.extend(decoded_keys)

                        # 搜索JSON中的嵌套数据
                        json_keys = self.extract_from_json_data(content, auth_file)
                        found_data.extend(json_keys)

        return found_data

    def extract_from_json_data(self, content, source_file):
        """从JSON数据中提取TOTP密钥"""
        found_keys = []

        # 搜索JSON中可能的密钥字段
        json_patterns = [
            r'"secret"[:\s]*"([A-Z2-7]{26})"',
            r'"key"[:\s]*"([A-Z2-7]{26})"',
            r'"totp"[:\s]*"([A-Z2-7]{26})"',
            r'"auth_key"[:\s]*"([A-Z2-7]{26})"',
            r'"backup_key"[:\s]*"([A-Z2-7]{26})"',
            r'"value"[:\s]*"([A-Z2-7]{26})"',
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if self.is_genuine_totp_key(match):
                    print(f"      � JSON中发现TOTP密钥: {match}")
                    found_keys.append({
                        'type': 'TOTP_26_JSON',
                        'value': match,
                        'length': 26,
                        'description': '⭐ JSON中的26字符TOTP密钥',
                        'source': f"JSON:{source_file}",
                        'formatted': ' '.join([match[i:i+4] for i in range(0, len(match), 4)]),
                        'context': self.get_context(content, match)
                    })

        return found_keys

    def search_fragmented_keys(self):
        """搜索可能分片存储的密钥"""
        print(f"\n🧩 搜索分片存储的密钥...")
        found_data = []

        # 搜索可能的密钥片段
        fragment_patterns = [
            r'[A-Z2-7]{6,13}',  # 密钥片段
            r'Z34O[A-Z2-7]*',   # 以Z34O开头的片段 (基于您的成功案例)
            r'[A-Z2-7]*3IXM',   # 以3IXM结尾的片段
        ]

        all_content = ""

        # 收集所有文件内容
        all_files_result = self.execute_command_with_retry("find /data/data/com.facebook.katana/ -type f -exec cat {} \\; 2>/dev/null")
        if all_files_result:
            all_content = all_files_result

        fragments = []
        for pattern in fragment_patterns:
            matches = re.findall(pattern, all_content)
            fragments.extend(matches)

        # 尝试组合片段
        if fragments:
            print(f"   🧩 发现 {len(fragments)} 个可能的密钥片段")
            combined_keys = self.try_combine_fragments(fragments)
            found_data.extend(combined_keys)

        return found_data

    def try_combine_fragments(self, fragments):
        """尝试组合密钥片段"""
        found_keys = []

        # 去重并排序
        unique_fragments = list(set(fragments))
        unique_fragments.sort(key=len, reverse=True)

        print(f"      🧩 尝试组合 {len(unique_fragments)} 个唯一片段")

        # 尝试各种组合方式
        for i, frag1 in enumerate(unique_fragments):
            for j, frag2 in enumerate(unique_fragments):
                if i != j:
                    # 尝试连接
                    combined = frag1 + frag2
                    if len(combined) == 26 and self.is_genuine_totp_key(combined):
                        print(f"         🎉 成功组合: {frag1} + {frag2} = {combined}")
                        found_keys.append({
                            'type': 'TOTP_26_COMBINED',
                            'value': combined,
                            'length': 26,
                            'description': '⭐ 组合得到的26字符TOTP密钥',
                            'source': f"片段组合({frag1}+{frag2})",
                            'formatted': ' '.join([combined[i:i+4] for i in range(0, len(combined), 4)]),
                            'fragments': [frag1, frag2]
                        })

        return found_keys

    def extract_26_char_totp_from_text(self, text, source):
        """从文本中提取26字符TOTP密钥"""
        found_keys = []

        # 专门匹配26字符Base32模式
        pattern = r'\b[A-Z2-7]{26}\b'
        matches = re.findall(pattern, text)

        for match in matches:
            if self.is_genuine_totp_key(match):
                key_info = {
                    'type': 'TOTP_26_STANDARD',
                    'value': match,
                    'length': 26,
                    'description': '⭐ 26字符标准TOTP密钥',
                    'source': source,
                    'formatted': ' '.join([match[i:i+4] for i in range(0, len(match), 4)]),
                    'context': self.get_context(text, match)
                }
                found_keys.append(key_info)

        return found_keys

    def is_genuine_totp_key(self, key):
        """判断是否为真正的TOTP密钥"""
        try:
            # 1. 基本Base32验证
            if not self.validate_2fa_data(key, 'TOTP_26_STANDARD'):
                return False

            # 2. 检查连续相同字符
            consecutive_count = 1
            max_consecutive = 1
            for i in range(1, len(key)):
                if key[i] == key[i-1]:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 1

            # 如果有超过3个连续相同字符，可能不是真正的密钥
            if max_consecutive > 3:
                return False

            return True

        except:
            return False

    def save_extraction_results(self, found_data):
        """保存提取结果"""
        try:
            # 分类数据
            for data_info in found_data:
                data_type = data_info['type']
                if data_type.startswith('TOTP_'):
                    self.extraction_results['totp_keys'].append(data_info)
                elif data_type.startswith('BACKUP_'):
                    self.extraction_results['backup_codes'].append(data_info)
                elif data_type in ['USER_ID', 'EMAIL', 'PHONE']:
                    self.extraction_results['user_info'][data_type] = data_info['value']
                else:
                    self.extraction_results['raw_data'].append(data_info)
            
            # 添加提取摘要
            self.extraction_results['extraction_summary'] = {
                'extraction_time': datetime.now().isoformat(),
                'emulator_index': self.emulator_index,
                'total_totp_keys': len(self.extraction_results['totp_keys']),
                'total_backup_codes': len(self.extraction_results['backup_codes']),
                'total_files_read': len(self.extraction_results['file_contents']),
                'user_info_found': bool(self.extraction_results['user_info'])
            }
            
            # 创建结果目录
            os.makedirs('facebook_2fa_results', exist_ok=True)
            
            # 保存到文件
            filename = f"facebook_2fa_results/fixed_fb_2fa_extract_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.extraction_results, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 提取结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    
    def run_extraction(self):
        """运行修复版提取流程"""
        print("🔧 修复版Facebook 2FA提取器")
        print("解决权限问题的Facebook双因素认证数据提取工具")
        print("=" * 60)
        
        try:
            all_found_data = []
            
            # 1. 测试Facebook目录访问
            if self.test_facebook_access():
                print("✅ Facebook目录访问测试通过")
            else:
                print("⚠️ Facebook目录访问受限，尝试其他方法")
            
            # 2. 直接提取重要文件
            direct_data = self.extract_facebook_files_directly()
            all_found_data.extend(direct_data)
            
            # 3. 使用find和grep搜索
            search_data = self.extract_using_find_and_grep()
            all_found_data.extend(search_data)

            # 4. 专门搜索26字符TOTP密钥
            totp_data = self.search_26_char_totp_specifically()
            all_found_data.extend(totp_data)
            
            # 4. 显示提取摘要
            totp_count = len([d for d in all_found_data if d['type'].startswith('TOTP_')])
            backup_count = len([d for d in all_found_data if d['type'].startswith('BACKUP_')])
            user_count = len([d for d in all_found_data if d['type'] in ['USER_ID', 'EMAIL', 'PHONE']])
            
            print(f"\n📊 提取摘要:")
            print("=" * 50)
            print(f"TOTP密钥: {totp_count} 个")
            print(f"备份代码: {backup_count} 个")
            print(f"用户信息: {user_count} 项")
            print(f"总数据: {len(all_found_data)} 项")
            
            # 5. 显示找到的重要数据
            if totp_count > 0:
                print(f"\n🔑 找到的TOTP密钥:")
                for data in all_found_data:
                    if data['type'].startswith('TOTP_'):
                        print(f"   {data['description']}: {data['value']}")
                        print(f"   来源: {data['source']}")
            
            if backup_count > 0:
                print(f"\n🎫 找到的备份代码:")
                for data in all_found_data[:10]:  # 显示前10个
                    if data['type'].startswith('BACKUP_'):
                        print(f"   {data['value']} (来源: {data['source']})")
            
            # 6. 保存结果
            result_file = self.save_extraction_results(all_found_data)
            
            if result_file:
                print(f"\n🎉 修复版Facebook 2FA提取完成!")
                return True
            else:
                print(f"\n❌ 保存结果失败")
                return False
                
        except Exception as e:
            print(f"❌ 提取流程失败: {e}")
            return False

def test_environment():
    """测试环境和权限"""
    print("🔧 环境测试工具")
    print("=" * 40)

    try:
        # 创建提取器实例
        extractor = FixedFacebook2FAExtractor(emulator_index=11)

        # 1. 测试基本连接
        print("\n1️⃣ 测试模拟器连接...")
        result = extractor.execute_command_with_retry("echo 'Hello World'")
        if result:
            print(f"✅ 模拟器连接正常: {result}")
        else:
            print("❌ 模拟器连接失败")
            return False

        # 2. 测试root权限
        print("\n2️⃣ 测试root权限...")
        result = extractor.execute_command_with_retry("whoami")
        print(f"当前用户: {result}")

        result = extractor.execute_command_with_retry("su -c 'whoami'")
        if result and "root" in result:
            print("✅ Root权限正常")
        else:
            print("❌ Root权限获取失败")
            return False

        # 3. 测试Facebook应用
        print("\n3️⃣ 检查Facebook应用...")
        result = extractor.execute_command_with_retry("pm list packages | grep facebook")
        if result and "com.facebook.katana" in result:
            print("✅ Facebook应用已安装")
        else:
            print("❌ Facebook应用未找到")
            return False

        # 4. 测试数据目录访问
        print("\n4️⃣ 测试数据目录访问...")
        result = extractor.execute_command_with_retry("ls -la /data/data/com.facebook.katana/")
        if result and "Permission denied" not in result:
            print("✅ 数据目录可访问")
            print(f"目录内容预览: {result[:200]}...")
        else:
            print("❌ 数据目录访问被拒绝")
            return False

        # 5. 检查关键文件
        print("\n5️⃣ 检查关键认证文件...")
        important_files = [
            "/data/data/com.facebook.katana/shared_prefs/",
            "/data/data/com.facebook.katana/files/",
            "/data/data/com.facebook.katana/databases/"
        ]

        for file_path in important_files:
            result = extractor.execute_command_with_retry(f"ls -la {file_path}")
            if result and "Permission denied" not in result:
                print(f"✅ {file_path} 可访问")
            else:
                print(f"❌ {file_path} 访问失败")

        print("\n🎉 环境测试完成！")
        return True

    except Exception as e:
        print(f"❌ 环境测试失败: {e}")
        return False

def manual_extraction_guide():
    """手动提取指导"""
    print("\n📋 手动提取指导")
    print("=" * 50)

    try:
        extractor = FixedFacebook2FAExtractor(emulator_index=11)

        print("🔍 步骤1: 查找所有可能的2FA相关文件")
        print("-" * 30)

        # 搜索所有包含可能2FA数据的文件
        search_commands = [
            "find /data/data/com.facebook.katana/ -name '*auth*' -type f",
            "find /data/data/com.facebook.katana/ -name '*secret*' -type f",
            "find /data/data/com.facebook.katana/ -name '*totp*' -type f",
            "find /data/data/com.facebook.katana/ -name '*2fa*' -type f",
            "find /data/data/com.facebook.katana/ -name '*.xml' -type f",
            "find /data/data/com.facebook.katana/ -name '*.db' -type f"
        ]

        all_files = set()
        for cmd in search_commands:
            result = extractor.execute_command_with_retry(cmd)
            if result:
                files = [f.strip() for f in result.split('\n') if f.strip()]
                all_files.update(files)
                print(f"找到文件: {len(files)} 个")

        print(f"\n📁 总共找到 {len(all_files)} 个相关文件")

        print("\n🔍 步骤2: 逐个检查文件内容")
        print("-" * 30)

        for i, file_path in enumerate(sorted(all_files)[:10]):  # 限制显示前10个
            print(f"\n📄 检查文件 {i+1}: {file_path}")

            # 获取文件大小
            size_result = extractor.execute_command_with_retry(f"wc -c {file_path}")
            if size_result:
                size = size_result.split()[0] if size_result.split() else "未知"
                print(f"   文件大小: {size} 字节")

            # 读取文件内容
            content = extractor.execute_command_with_retry(f"cat {file_path}")
            if content:
                print(f"   内容预览: {content[:100]}...")

                # 搜索可能的TOTP模式
                import re
                totp_patterns = [
                    r'[A-Z2-7]{26}',  # 26字符Base32
                    r'[A-Z2-7]{32}',  # 32字符Base32
                    r'[a-fA-F0-9]{52}',  # 52字符十六进制
                    r'[a-fA-F0-9]{40}',  # 40字符十六进制
                ]

                for pattern in totp_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"   🔑 发现可能的密钥: {matches[:3]}...")  # 显示前3个
            else:
                print("   ❌ 无法读取文件内容")

        print("\n💡 手动检查建议:")
        print("1. 重点关注 shared_prefs/ 目录下的XML文件")
        print("2. 检查 databases/ 目录下的数据库文件")
        print("3. 查看 files/ 目录下的认证相关文件")
        print("4. 使用 strings 命令提取二进制文件中的可读字符串")

    except Exception as e:
        print(f"❌ 手动提取指导失败: {e}")

def main():
    """主函数"""
    print("🔧 修复版Facebook 2FA提取器")
    print("=" * 40)

    while True:
        print("\n请选择操作:")
        print("1. 环境测试")
        print("2. 手动提取指导")
        print("3. 运行完整提取")
        print("4. 退出")

        choice = input("\n请输入选择 (1-4): ").strip()

        if choice == "1":
            test_environment()
        elif choice == "2":
            manual_extraction_guide()
        elif choice == "3":
            try:
                # 创建提取器实例
                extractor = FixedFacebook2FAExtractor(emulator_index=11)

                # 运行提取流程
                success = extractor.run_extraction()

                if success:
                    print("\n🎉 修复版Facebook 2FA提取成功！")
                    print("📁 请查看facebook_2fa_results目录中的结果文件")
                else:
                    print("\n❌ 修复版Facebook 2FA提取失败")

            except KeyboardInterrupt:
                print("\n\n⚠️ 用户中断操作")
            except Exception as e:
                print(f"❌ 程序执行失败: {e}")
        elif choice == "4":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
